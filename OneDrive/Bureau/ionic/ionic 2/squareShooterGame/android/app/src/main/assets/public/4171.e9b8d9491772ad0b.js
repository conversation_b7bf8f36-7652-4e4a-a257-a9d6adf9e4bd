"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4171],{4171:(_,a,t)=>{t.r(a),t.d(a,{ion_infinite_scroll:()=>m,ion_infinite_scroll_content:()=>x});var d=t(467),n=t(2734),o=t(4657),f=t(6640);t(1837);const m=(()=>{let s=class{constructor(i){(0,n.r)(this,i),this.ionInfinite=(0,n.d)(this,"ionInfinite",7),this.thrPx=0,this.thrPc=0,this.didFire=!1,this.isBusy=!1,this.isLoading=!1,this.threshold="15%",this.disabled=!1,this.position="bottom",this.onScroll=()=>{const e=this.scrollEl;if(!e||!this.canStart())return 1;const l=this.el.offsetHeight;if(0===l)return 2;const r=e.scrollTop,h=e.offsetHeight,g=0!==this.thrPc?h*this.thrPc:this.thrPx;return("bottom"===this.position?e.scrollHeight-l-r-g-h:r-l-g)<0&&!this.didFire?(this.isLoading=!0,this.didFire=!0,this.ionInfinite.emit(),3):4}}thresholdChanged(){const i=this.threshold;i.lastIndexOf("%")>-1?(this.thrPx=0,this.thrPc=parseFloat(i)/100):(this.thrPx=parseFloat(i),this.thrPc=0)}disabledChanged(){const i=this.disabled;i&&(this.isLoading=!1,this.isBusy=!1),this.enableScrollEvents(!i)}connectedCallback(){var i=this;return(0,d.A)(function*(){const e=(0,o.f)(i.el);e?(i.scrollEl=yield(0,o.g)(e),i.thresholdChanged(),i.disabledChanged(),"top"===i.position&&(0,n.w)(()=>{i.scrollEl&&(i.scrollEl.scrollTop=i.scrollEl.scrollHeight-i.scrollEl.clientHeight)})):(0,o.p)(i.el)})()}disconnectedCallback(){this.enableScrollEvents(!1),this.scrollEl=void 0}complete(){var i=this;return(0,d.A)(function*(){const e=i.scrollEl;if(i.isLoading&&e)if(i.isLoading=!1,"top"===i.position){i.isBusy=!0;const l=e.scrollHeight-e.scrollTop;requestAnimationFrame(()=>{(0,n.f)(()=>{const c=e.scrollHeight-l;requestAnimationFrame(()=>{(0,n.w)(()=>{e.scrollTop=c,i.isBusy=!1,i.didFire=!1})})})})}else i.didFire=!1})()}canStart(){return!(this.disabled||this.isBusy||!this.scrollEl||this.isLoading)}enableScrollEvents(i){this.scrollEl&&(i?this.scrollEl.addEventListener("scroll",this.onScroll):this.scrollEl.removeEventListener("scroll",this.onScroll))}render(){const i=(0,n.e)(this),e=this.disabled;return(0,n.h)(n.j,{key:"e844956795f69be33396ce4480aa7a54ad01b28c",class:{[i]:!0,"infinite-scroll-loading":this.isLoading,"infinite-scroll-enabled":!e}})}get el(){return(0,n.k)(this)}static get watchers(){return{threshold:["thresholdChanged"],disabled:["disabledChanged"]}}};return s.style="ion-infinite-scroll{display:none;width:100%}.infinite-scroll-enabled{display:block}",s})(),x=(()=>{let s=class{constructor(i){(0,n.r)(this,i),this.customHTMLEnabled=n.l.get("innerHTMLTemplatesEnabled",f.E)}componentDidLoad(){if(void 0===this.loadingSpinner){const i=(0,n.e)(this);this.loadingSpinner=n.l.get("infiniteLoadingSpinner",n.l.get("spinner","ios"===i?"lines":"crescent"))}}renderLoadingText(){const{customHTMLEnabled:i,loadingText:e}=this;return i?(0,n.h)("div",{class:"infinite-loading-text",innerHTML:(0,f.a)(e)}):(0,n.h)("div",{class:"infinite-loading-text"},this.loadingText)}render(){const i=(0,n.e)(this);return(0,n.h)(n.j,{key:"7c16060dcfe2a0b0fb3e2f8f4c449589a76f1baa",class:{[i]:!0,[`infinite-scroll-content-${i}`]:!0}},(0,n.h)("div",{key:"a94f4d8746e053dc718f97520bd7e48cb316443a",class:"infinite-loading"},this.loadingSpinner&&(0,n.h)("div",{key:"10143d5d2a50a2a2bc5de1cee8e7ab51263bcf23",class:"infinite-loading-spinner"},(0,n.h)("ion-spinner",{key:"8846e88191690d9c61a0b462889ed56fbfed8b0d",name:this.loadingSpinner})),void 0!==this.loadingText&&this.renderLoadingText()))}};return s.style={ios:"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-ios .infinite-loading-text{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-lines-small-ios line,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-ios .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-ios .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}",md:"ion-infinite-scroll-content{display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:center;justify-content:center;min-height:84px;text-align:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.infinite-loading{margin-left:0;margin-right:0;margin-top:0;margin-bottom:32px;display:none;width:100%}.infinite-loading-text{-webkit-margin-start:32px;margin-inline-start:32px;-webkit-margin-end:32px;margin-inline-end:32px;margin-top:4px;margin-bottom:0}.infinite-scroll-loading ion-infinite-scroll-content>.infinite-loading{display:block}.infinite-scroll-content-md .infinite-loading-text{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-lines-small-md line,.infinite-scroll-content-md .infinite-loading-spinner .spinner-crescent circle{stroke:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.infinite-scroll-content-md .infinite-loading-spinner .spinner-bubbles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-circles circle,.infinite-scroll-content-md .infinite-loading-spinner .spinner-dots circle{fill:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}"},s})()}}]);