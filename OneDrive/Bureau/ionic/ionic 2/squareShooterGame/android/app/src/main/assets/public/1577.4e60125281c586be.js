"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[1577],{1577:(h,u,r)=>{r.r(u),r.d(u,{ion_text:()=>i});var o=r(2734),c=r(4576);const i=(()=>{let a=class{constructor(s){(0,o.r)(this,s)}render(){const s=(0,o.e)(this);return(0,o.h)(o.j,{key:"361035eae7b92dc109794348d39bad2f596eb6be",class:(0,c.c)(this.color,{[s]:!0})},(0,o.h)("slot",{key:"c7b8835cf485ba9ecd73298f0529276ce1ea0852"}))}};return a.style=":host(.ion-color){color:var(--ion-color-base)}",a})()},4576:(h,u,r)=>{r.d(u,{c:()=>_,g:()=>a,h:()=>c,o:()=>m});var o=r(467);const c=(t,e)=>null!==e.closest(t),_=(t,e)=>"string"==typeof t&&t.length>0?Object.assign({"ion-color":!0,[`ion-color-${t}`]:!0},e):e,a=t=>{const e={};return(t=>void 0!==t?(Array.isArray(t)?t:t.split(" ")).filter(n=>null!=n).map(n=>n.trim()).filter(n=>""!==n):[])(t).forEach(n=>e[n]=!0),e},s=/^[a-z][a-z0-9+\-.]*:/,m=function(){var t=(0,o.A)(function*(e,n,f,d){if(null!=e&&"#"!==e[0]&&!s.test(e)){const l=document.querySelector("ion-router");if(l)return n?.preventDefault(),l.push(e,f,d)}return!1});return function(n,f,d,l){return t.apply(this,arguments)}}()}}]);