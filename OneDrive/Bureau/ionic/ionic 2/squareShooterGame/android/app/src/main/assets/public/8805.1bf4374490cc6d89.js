"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[8805],{4576:(y,c,a)=>{a.d(c,{c:()=>p,g:()=>f,h:()=>m,o:()=>h});var s=a(467);const m=(n,e)=>null!==e.closest(n),p=(n,e)=>"string"==typeof n&&n.length>0?Object.assign({"ion-color":!0,[`ion-color-${n}`]:!0},e):e,f=n=>{const e={};return(n=>void 0!==n?(Array.isArray(n)?n:n.split(" ")).filter(t=>null!=t).map(t=>t.trim()).filter(t=>""!==t):[])(n).forEach(t=>e[t]=!0),e},d=/^[a-z][a-z0-9+\-.]*:/,h=function(){var n=(0,s.A)(function*(e,t,i,r){if(null!=e&&"#"!==e[0]&&!d.test(e)){const o=document.querySelector("ion-router");if(o)return t?.preventDefault(),o.push(e,i,r)}return!1});return function(t,i,r,o){return n.apply(this,arguments)}}()},8805:(y,c,a)=>{a.r(c),a.d(c,{ion_spinner:()=>f});var s=a(2734),m=a(4576),p=a(7764);const f=class{constructor(n){(0,s.r)(this,n),this.paused=!1}getName(){const n=this.name||s.l.get("spinner"),e=(0,s.e)(this);return n||("ios"===e?"lines":"circular")}render(){var n;const e=this,t=(0,s.e)(e),i=e.getName(),r=null!==(n=p.S[i])&&void 0!==n?n:p.S.lines,o="number"==typeof e.duration&&e.duration>10?e.duration:r.dur,k=[];if(void 0!==r.circles)for(let l=0;l<r.circles;l++)k.push(d(r,o,l,r.circles));else if(void 0!==r.lines)for(let l=0;l<r.lines;l++)k.push(h(r,o,l,r.lines));return(0,s.h)(s.j,{key:"a33d6421fcc885995fbc7a348516525f68ca496c",class:(0,m.c)(e.color,{[t]:!0,[`spinner-${i}`]:!0,"spinner-paused":e.paused||s.l.getBoolean("_testing")}),role:"progressbar",style:r.elmDuration?{animationDuration:o+"ms"}:{}},k)}},d=(n,e,t,i)=>{const r=n.fn(e,t,i);return r.style["animation-duration"]=e+"ms",(0,s.h)("svg",{viewBox:r.viewBox||"0 0 64 64",style:r.style},(0,s.h)("circle",{transform:r.transform||"translate(32,32)",cx:r.cx,cy:r.cy,r:r.r,style:n.elmDuration?{animationDuration:e+"ms"}:{}}))},h=(n,e,t,i)=>{const r=n.fn(e,t,i);return r.style["animation-duration"]=e+"ms",(0,s.h)("svg",{viewBox:r.viewBox||"0 0 64 64",style:r.style},(0,s.h)("line",{transform:"translate(32,32)",y1:r.y1,y2:r.y2}))};f.style=":host{display:inline-block;position:relative;width:28px;height:28px;color:var(--color);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.ion-color){color:var(--ion-color-base)}svg{-webkit-transform-origin:center;transform-origin:center;position:absolute;top:0;left:0;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0)}:host-context([dir=rtl]) svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}[dir=rtl] svg{-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}@supports selector(:dir(rtl)){svg:dir(rtl){-webkit-transform-origin:calc(100% - center);transform-origin:calc(100% - center)}}:host(.spinner-lines) line,:host(.spinner-lines-small) line{stroke-width:7px}:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-width:4px}:host(.spinner-lines) line,:host(.spinner-lines-small) line,:host(.spinner-lines-sharp) line,:host(.spinner-lines-sharp-small) line{stroke-linecap:round;stroke:currentColor}:host(.spinner-lines) svg,:host(.spinner-lines-small) svg,:host(.spinner-lines-sharp) svg,:host(.spinner-lines-sharp-small) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite}:host(.spinner-bubbles) svg{-webkit-animation:spinner-scale-out 1s linear infinite;animation:spinner-scale-out 1s linear infinite;fill:currentColor}:host(.spinner-circles) svg{-webkit-animation:spinner-fade-out 1s linear infinite;animation:spinner-fade-out 1s linear infinite;fill:currentColor}:host(.spinner-crescent) circle{fill:transparent;stroke-width:4px;stroke-dasharray:128px;stroke-dashoffset:82px;stroke:currentColor}:host(.spinner-crescent) svg{-webkit-animation:spinner-rotate 1s linear infinite;animation:spinner-rotate 1s linear infinite}:host(.spinner-dots) circle{stroke-width:0;fill:currentColor}:host(.spinner-dots) svg{-webkit-animation:spinner-dots 1s linear infinite;animation:spinner-dots 1s linear infinite}:host(.spinner-circular) svg{-webkit-animation:spinner-circular linear infinite;animation:spinner-circular linear infinite}:host(.spinner-circular) circle{-webkit-animation:spinner-circular-inner ease-in-out infinite;animation:spinner-circular-inner ease-in-out infinite;stroke:currentColor;stroke-dasharray:80px, 200px;stroke-dashoffset:0px;stroke-width:5.6;fill:none}:host(.spinner-paused),:host(.spinner-paused) svg,:host(.spinner-paused) circle{-webkit-animation-play-state:paused;animation-play-state:paused}@-webkit-keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@keyframes spinner-fade-out{0%{opacity:1}100%{opacity:0}}@-webkit-keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@keyframes spinner-scale-out{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1)}100%{-webkit-transform:scale(0, 0);transform:scale(0, 0)}}@-webkit-keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-rotate{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@keyframes spinner-dots{0%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}50%{-webkit-transform:scale(0.4, 0.4);transform:scale(0.4, 0.4);opacity:0.3}100%{-webkit-transform:scale(1, 1);transform:scale(1, 1);opacity:0.9}}@-webkit-keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spinner-circular{100%{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-webkit-keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}@keyframes spinner-circular-inner{0%{stroke-dasharray:1px, 200px;stroke-dashoffset:0px}50%{stroke-dasharray:100px, 200px;stroke-dashoffset:-15px}100%{stroke-dasharray:100px, 200px;stroke-dashoffset:-125px}}"}}]);