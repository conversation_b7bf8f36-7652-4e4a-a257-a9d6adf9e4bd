"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[4183],{4183:(f,s,e)=>{e.r(s),e.d(s,{ion_img:()=>o});var i=e(2734),n=e(1837);const o=class{constructor(t){(0,i.r)(this,t),this.ionImgWillLoad=(0,i.d)(this,"ionImgWillLoad",7),this.ionImgDidLoad=(0,i.d)(this,"ionImgDidLoad",7),this.ionError=(0,i.d)(this,"ionError",7),this.inheritedAttributes={},this.onLoad=()=>{this.ionImgDidLoad.emit()},this.onError=()=>{this.ionError.emit()}}srcChanged(){this.addIO()}componentWillLoad(){this.inheritedAttributes=(0,n.b)(this.el,["draggable"])}componentDidLoad(){this.addIO()}addIO(){void 0!==this.src&&(typeof window<"u"&&"IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"isIntersecting"in window.IntersectionObserverEntry.prototype?(this.removeIO(),this.io=new IntersectionObserver(t=>{t[t.length-1].isIntersecting&&(this.load(),this.removeIO())}),this.io.observe(this.el)):setTimeout(()=>this.load(),200))}load(){this.loadError=this.onError,this.loadSrc=this.src,this.ionImgWillLoad.emit()}removeIO(){this.io&&(this.io.disconnect(),this.io=void 0)}render(){const{loadSrc:t,alt:a,onLoad:h,loadError:c,inheritedAttributes:l}=this,{draggable:g}=l;return(0,i.h)(i.j,{key:"da600442894427dee1974a28e545613afac69fca",class:(0,i.e)(this)},(0,i.h)("img",{key:"16df0c7069af86c0fa7ce5af598bc0f63b4eb71a",decoding:"async",src:t,alt:a,onLoad:h,onError:c,part:"image",draggable:d(g)}))}get el(){return(0,i.k)(this)}static get watchers(){return{src:["srcChanged"]}}},d=t=>{switch(t){case"true":return!0;case"false":return!1;default:return}};o.style=":host{display:block;-o-object-fit:contain;object-fit:contain}img{display:block;width:100%;height:100%;-o-object-fit:inherit;object-fit:inherit;-o-object-position:inherit;object-position:inherit}"}}]);