"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[2560],{2560:(V,w,h)=>{h.r(w),h.d(w,{ion_segment:()=>c,ion_segment_button:()=>E});var y=h(467),r=h(2734),f=h(1837),C=h(647),v=h(4576);const c=(()=>{let m=class{constructor(t){(0,r.r)(this,t),this.ionChange=(0,r.d)(this,"ionChange",7),this.ionSelect=(0,r.d)(this,"ionSelect",7),this.ionStyle=(0,r.d)(this,"ionStyle",7),this.segmentViewEl=null,this.activated=!1,this.disabled=!1,this.scrollable=!1,this.swipeGesture=!0,this.selectOnFocus=!1,this.onClick=o=>{const n=o.target,e=this.checked;"ION-SEGMENT"!==n.tagName&&(this.value=n.value,n!==e&&this.emitValueChange(),this.segmentViewEl?(this.updateSegmentView(),this.scrollable&&e&&this.checkButton(e,n)):(this.scrollable||!this.swipeGesture)&&(e?this.checkButton(e,n):this.setCheckedClasses()))},this.onSlottedItemsChange=()=>{this.valueChanged(this.value)},this.getSegmentButton=o=>{var n,e;const i=this.getButtons().filter(a=>!a.disabled),s=i.findIndex(a=>a===document.activeElement);switch(o){case"first":return i[0];case"last":return i[i.length-1];case"next":return null!==(n=i[s+1])&&void 0!==n?n:i[0];case"previous":return null!==(e=i[s-1])&&void 0!==e?e:i[i.length-1];default:return null}}}colorChanged(t,o){(void 0===o&&void 0!==t||void 0!==o&&void 0===t)&&this.emitStyle()}swipeGestureChanged(){this.gestureChanged()}valueChanged(t,o){if(this.segmentViewEl&&void 0===t)this.value=this.getButtons()[0].value;else{if(void 0!==o&&void 0!==t){const n=this.getButtons(),e=n.find(s=>s.value===o),i=n.find(s=>s.value===t);e&&i&&(this.segmentViewEl?!1!==this.triggerScrollOnValueChange&&this.updateSegmentView():this.checkButton(e,i))}else void 0!==t&&void 0===o&&this.segmentViewEl&&this.updateSegmentView();this.ionSelect.emit({value:t}),this.segmentViewEl||this.scrollActiveButtonIntoView(),this.triggerScrollOnValueChange=void 0}}disabledChanged(){if(this.gestureChanged(),this.segmentViewEl)this.segmentViewEl.disabled=this.disabled;else{const t=this.getButtons();for(const o of t)o.disabled=this.disabled}}gestureChanged(){this.gesture&&this.gesture.enable(!this.scrollable&&!this.disabled&&this.swipeGesture)}connectedCallback(){this.emitStyle(),this.segmentViewEl=this.getSegmentView()}disconnectedCallback(){this.segmentViewEl=null}componentWillLoad(){this.emitStyle()}componentDidLoad(){var t=this;return(0,y.A)(function*(){t.segmentViewEl=t.getSegmentView(),t.setCheckedClasses(),(0,f.r)(()=>{t.scrollActiveButtonIntoView(!1)}),t.gesture=(yield Promise.resolve().then(h.bind(h,6011))).createGesture({el:t.el,gestureName:"segment",gesturePriority:100,threshold:0,passive:!1,onStart:o=>t.onStart(o),onMove:o=>t.onMove(o),onEnd:o=>t.onEnd(o)}),t.gestureChanged(),t.disabled&&t.disabledChanged(),t.updateSegmentView(!1)})()}onStart(t){this.valueBeforeGesture=this.value,this.activate(t)}onMove(t){this.setNextIndex(t)}onEnd(t){this.setActivated(!1),this.setNextIndex(t,!0),t.event.stopImmediatePropagation();const o=this.value;void 0!==o&&this.valueBeforeGesture!==o&&(this.emitValueChange(),this.updateSegmentView()),this.valueBeforeGesture=void 0}emitValueChange(){const{value:t}=this;this.ionChange.emit({value:t})}getButtons(){return Array.from(this.el.querySelectorAll("ion-segment-button"))}get checked(){return this.getButtons().find(t=>t.value===this.value)}setActivated(t){this.getButtons().forEach(n=>{n.classList.toggle("segment-button-activated",t)}),this.activated=t}activate(t){const o=t.event.target,e=this.getButtons().find(i=>i.value===this.value);"ION-SEGMENT-BUTTON"===o.tagName&&(e||(this.value=o.value,this.setCheckedClasses()),this.value===o.value&&this.setActivated(!0))}getIndicator(t){return(t.shadowRoot||t).querySelector(".segment-button-indicator")}checkButton(t,o){const n=this.getIndicator(t),e=this.getIndicator(o);if(null===n||null===e)return;const i=n.getBoundingClientRect(),s=e.getBoundingClientRect(),g=`translate3d(${i.left-s.left}px, 0, 0) scaleX(${i.width/s.width})`;(0,r.w)(()=>{e.classList.remove("segment-button-indicator-animated"),e.style.setProperty("transform",g),e.getBoundingClientRect(),e.classList.add("segment-button-indicator-animated"),e.style.setProperty("transform",""),this.scrollActiveButtonIntoView(!0)}),this.value=o.value,this.setCheckedClasses()}setCheckedClasses(){const t=this.getButtons(),n=t.findIndex(e=>e.value===this.value)+1;for(const e of t)e.classList.remove("segment-button-after-checked");n<t.length&&t[n].classList.add("segment-button-after-checked")}getSegmentView(){const o=this.getButtons().find(e=>e.contentId),n=document.querySelector(`ion-segment-content[id="${o?.contentId}"]`);return n?.closest("ion-segment-view")}handleSegmentViewScroll(t){const{scrollRatio:o,isManualScroll:n}=t.detail;if(!n)return;const e=t.target,i=this.segmentViewEl,s=this.el;if(t.composedPath().includes(i)||e?.contains(s)){const a=this.getButtons();if(!a.length)return;const d=a.findIndex(p=>p.value===this.value),g=a[d],b=Math.round(o*(a.length-1));(void 0===this.lastNextIndex||this.lastNextIndex!==b)&&(this.lastNextIndex=b,this.triggerScrollOnValueChange=!1,this.checkButton(g,a[b]),this.emitValueChange())}}updateSegmentView(t=!0){const n=this.getButtons().find(i=>i.value===this.value);if(!n?.contentId)return;const e=this.segmentViewEl;e&&e.setContent(n.contentId,t)}scrollActiveButtonIntoView(t=!0){const{scrollable:o,value:n,el:e}=this;if(o){const s=this.getButtons().find(a=>a.value===n);if(void 0!==s){const a=e.getBoundingClientRect(),d=s.getBoundingClientRect();e.scrollTo({top:0,left:e.scrollLeft+(d.x-a.x-a.width/2+d.width/2),behavior:t?"smooth":"instant"})}}}setNextIndex(t,o=!1){const n=(0,C.i)(this.el),e=this.activated,i=this.getButtons(),s=i.findIndex(x=>x.value===this.value),a=i[s];let d,g;if(-1===s)return;const b=a.getBoundingClientRect(),p=b.left,I=b.width,k=t.currentX,O=b.top+b.height/2,z=this.el.getRootNode().elementFromPoint(k,O);if(e&&!o){if(n?k>p+I:k<p){const x=s-1;x>=0&&(g=x)}else if((n?k<p:k>p+I)&&e&&!o){const x=s+1;x<i.length&&(g=x)}void 0!==g&&!i[g].disabled&&(d=i[g])}if(!e&&o&&(d=z),null!=d){if("ION-SEGMENT"===d.tagName)return!1;a!==d&&this.checkButton(a,d)}return!0}emitStyle(){this.ionStyle.emit({segment:!0})}onKeyDown(t){const o=(0,C.i)(this.el);let e,n=this.selectOnFocus;switch(t.key){case"ArrowRight":t.preventDefault(),e=this.getSegmentButton(o?"previous":"next");break;case"ArrowLeft":t.preventDefault(),e=this.getSegmentButton(o?"next":"previous");break;case"Home":t.preventDefault(),e=this.getSegmentButton("first");break;case"End":t.preventDefault(),e=this.getSegmentButton("last");break;case" ":case"Enter":t.preventDefault(),e=document.activeElement,n=!0}if(e){if(n){const i=this.checked;this.checkButton(i||e,e),e!==i&&this.emitValueChange()}e.setFocus()}}render(){const t=(0,r.e)(this);return(0,r.h)(r.j,{key:"e67ed512739cf2cfe657b0c44ebc3dfb1e9fbb79",role:"tablist",onClick:this.onClick,class:(0,v.c)(this.color,{[t]:!0,"in-toolbar":(0,v.h)("ion-toolbar",this.el),"in-toolbar-color":(0,v.h)("ion-toolbar[color]",this.el),"segment-activated":this.activated,"segment-disabled":this.disabled,"segment-scrollable":this.scrollable})},(0,r.h)("slot",{key:"804d8acfcb9abfeeee38244b015fbc5c36330b9e",onSlotchange:this.onSlottedItemsChange}))}get el(){return(0,r.k)(this)}static get watchers(){return{color:["colorChanged"],swipeGesture:["swipeGestureChanged"],value:["valueChanged"],disabled:["disabledChanged"]}}};return m.style={ios:":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.065);border-radius:8px;overflow:hidden;z-index:0}:host(.ion-color){background:rgba(var(--ion-color-base-rgb), 0.065)}:host(.in-toolbar){-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;margin-top:0;margin-bottom:0;width:auto}:host(.in-toolbar:not(.ion-color)){background:var(--ion-toolbar-segment-background, var(--background))}:host(.in-toolbar-color:not(.ion-color)){background:rgba(var(--ion-color-contrast-rgb), 0.11)}",md:":host{--ripple-color:currentColor;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:grid;grid-auto-columns:1fr;position:relative;-ms-flex-align:stretch;align-items:stretch;-ms-flex-pack:center;justify-content:center;width:100%;background:var(--background);font-family:var(--ion-font-family, inherit);text-align:center;contain:paint;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}:host(.segment-scrollable){-ms-flex-pack:start;justify-content:start;width:auto;overflow-x:auto;grid-auto-columns:minmax(-webkit-min-content, 1fr);grid-auto-columns:minmax(min-content, 1fr)}:host(.segment-scrollable::-webkit-scrollbar){display:none}:host{--background:transparent;grid-auto-columns:minmax(auto, 360px)}:host(.in-toolbar){min-height:var(--min-height)}:host(.segment-scrollable) ::slotted(ion-segment-button){min-width:auto}"},m})();let S=0;const E=(()=>{let m=class{constructor(t){(0,r.r)(this,t),this.segmentEl=null,this.inheritedAttributes={},this.checked=!1,this.disabled=!1,this.layout="icon-top",this.type="button",this.value="ion-sb-"+S++,this.updateStyle=()=>{(0,r.n)(this)},this.updateState=()=>{const{segmentEl:o}=this;o&&(this.checked=o.value===this.value,o.disabled&&(this.disabled=!0))}}valueChanged(){this.updateState()}connectedCallback(){const t=this.segmentEl=this.el.closest("ion-segment");t&&(this.updateState(),(0,f.f)(t,"ionSelect",this.updateState),(0,f.f)(t,"ionStyle",this.updateStyle)),this.contentId&&this.disabled&&((0,r.m)("[ion-segment-button] - Segment buttons cannot be disabled when associated with an <ion-segment-content>."),this.disabled=!1)}disconnectedCallback(){const t=this.segmentEl;t&&((0,f.m)(t,"ionSelect",this.updateState),(0,f.m)(t,"ionStyle",this.updateStyle),this.segmentEl=null)}componentWillLoad(){if(this.inheritedAttributes=Object.assign({},(0,f.b)(this.el,["aria-label"])),!this.contentId)return;const t=document.getElementById(this.contentId);t?"ION-SEGMENT-CONTENT"===t.tagName||(0,r.o)(`[ion-segment-button] - Element with id="${this.contentId}" is not an <ion-segment-content> element.`):(0,r.o)(`[ion-segment-button] - Unable to find Segment Content with id="${this.contentId}".`)}get hasLabel(){return!!this.el.querySelector("ion-label")}get hasIcon(){return!!this.el.querySelector("ion-icon")}setFocus(){var t=this;return(0,y.A)(function*(){const{nativeEl:o}=t;void 0!==o&&o.focus()})()}render(){const{checked:t,type:o,disabled:n,hasIcon:e,hasLabel:i,layout:s,segmentEl:a}=this,d=(0,r.e)(this);return(0,r.h)(r.j,{key:"26cb7ee90455bcaa6416125802d7e5729fa05b5b",class:{[d]:!0,"in-toolbar":(0,v.h)("ion-toolbar",this.el),"in-toolbar-color":(0,v.h)("ion-toolbar[color]",this.el),"in-segment":(0,v.h)("ion-segment",this.el),"in-segment-color":void 0!==a?.color,"segment-button-has-label":i,"segment-button-has-icon":e,"segment-button-has-label-only":i&&!e,"segment-button-has-icon-only":e&&!i,"segment-button-disabled":n,"segment-button-checked":t,[`segment-button-layout-${s}`]:!0,"ion-activatable":!0,"ion-activatable-instant":!0,"ion-focusable":!0}},(0,r.h)("button",Object.assign({key:"75add37f11c107d1e2cfdb154e08004e9579e863","aria-selected":t?"true":"false",role:"tab",ref:b=>this.nativeEl=b,type:o,class:"button-native",part:"native",disabled:n},this.inheritedAttributes),(0,r.h)("span",{key:"8e720d2a3e304903685bf09d226a64e944d78a22",class:"button-inner"},(0,r.h)("slot",{key:"c8e7b3ebf8f03042a1001155643b585283c73c65"})),"md"===d&&(0,r.h)("ion-ripple-effect",{key:"3586ac317b8d82c92b0ccfbfae42f8778612321b"})),(0,r.h)("div",{key:"9cf93957da9e8dc333c8b05327bb903385b1c5f4",part:"indicator",class:"segment-button-indicator segment-button-indicator-animated"},(0,r.h)("div",{key:"d3b6f0b3860ec6896b46703f64ed1cc8c75612e3",part:"indicator-background",class:"segment-button-indicator-background"})))}get el(){return(0,r.k)(this)}static get watchers(){return{value:["valueChanged"]}}};return m.style={ios:':host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:none;--background-hover-opacity:0;--background-focused:none;--background-focused-opacity:0;--border-radius:7px;--border-width:1px;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.12);--border-style:solid;--indicator-box-shadow:0 0 5px rgba(0, 0, 0, 0.16);--indicator-color:var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff)));--indicator-height:100%;--indicator-transition:transform 260ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--transition:100ms all linear;--padding-top:0;--padding-end:13px;--padding-bottom:0;--padding-start:13px;margin-top:2px;margin-bottom:2px;position:relative;-ms-flex-direction:row;flex-direction:row;min-width:70px;min-height:28px;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);font-size:13px;font-weight:450;line-height:37px}:host::before{margin-left:0;margin-right:0;margin-top:5px;margin-bottom:5px;-webkit-transition:160ms opacity ease-in-out;transition:160ms opacity ease-in-out;-webkit-transition-delay:100ms;transition-delay:100ms;border-left:var(--border-width) var(--border-style) var(--border-color);content:"";opacity:1;will-change:opacity}:host(:first-of-type)::before{border-left-color:transparent}:host(.segment-button-disabled){opacity:0.3}::slotted(ion-icon){font-size:24px}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:2px;margin-inline-end:2px}.segment-button-indicator{-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;left:0;right:0;top:0;bottom:0}.segment-button-indicator-background{border-radius:var(--border-radius);background:var(--indicator-color)}.segment-button-indicator-background{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked)::before,:host(.segment-button-after-checked)::before{opacity:0}:host(.segment-button-checked){z-index:-1}:host(.segment-button-activated){--indicator-transform:scale(0.95)}:host(.ion-focused) .button-native{opacity:0.7}@media (any-hover: hover){:host(:hover) .button-native{opacity:0.5}:host(.segment-button-checked:hover) .button-native{opacity:1}}:host(.in-segment-color){background:none;color:var(--ion-text-color, #000)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff)))}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native,:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-text-color, #000)}}:host(.in-toolbar:not(.in-segment-color)){--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, var(--ion-toolbar-color), initial);--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-toolbar-color), initial);--indicator-color:var(--ion-toolbar-segment-indicator-color, var(--ion-color-step-350, var(--ion-background-color-step-350, var(--ion-background-color, #fff))))}:host(.in-toolbar-color) .segment-button-indicator-background{background:var(--ion-color-contrast)}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-base)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color):hover) .button-native{color:var(--ion-color-contrast)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color):hover) .button-native{color:var(--ion-color-base)}}',md:':host{--color:initial;--color-hover:var(--color);--color-checked:var(--color);--color-disabled:var(--color);--padding-start:0;--padding-end:0;--padding-top:0;--padding-bottom:0;border-radius:var(--border-radius);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:column;flex-direction:column;height:auto;background:var(--background);color:var(--color);text-decoration:none;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;grid-row:1;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:0;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;min-width:inherit;max-width:inherit;height:auto;min-height:inherit;max-height:inherit;-webkit-transition:var(--transition);transition:var(--transition);border:none;outline:none;background:transparent;contain:content;pointer-events:none;overflow:hidden;z-index:2}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;z-index:1}:host(.segment-button-checked){background:var(--background-checked);color:var(--color-checked)}:host(.segment-button-disabled){cursor:default;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}:host(:focus){outline:none}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity)}:host(.segment-button-checked:hover) .button-native{color:var(--color-checked)}}::slotted(ion-icon){-ms-flex-negative:0;flex-shrink:0;-ms-flex-order:-1;order:-1;pointer-events:none}::slotted(ion-label){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;line-height:22px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host(.segment-button-layout-icon-top) .button-native{-ms-flex-direction:column;flex-direction:column}:host(.segment-button-layout-icon-start) .button-native{-ms-flex-direction:row;flex-direction:row}:host(.segment-button-layout-icon-end) .button-native{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.segment-button-layout-icon-bottom) .button-native{-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.segment-button-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.segment-button-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color, var(--color-checked))}.segment-button-indicator{-webkit-transform-origin:left;transform-origin:left;position:absolute;opacity:0;-webkit-box-sizing:border-box;box-sizing:border-box;will-change:transform, opacity;pointer-events:none}.segment-button-indicator-background{width:100%;height:var(--indicator-height);-webkit-transform:var(--indicator-transform);transform:var(--indicator-transform);-webkit-box-shadow:var(--indicator-box-shadow);box-shadow:var(--indicator-box-shadow);pointer-events:none}.segment-button-indicator-animated{-webkit-transition:var(--indicator-transition);transition:var(--indicator-transition)}:host(.segment-button-checked) .segment-button-indicator{opacity:1}@media (prefers-reduced-motion: reduce){.segment-button-indicator-background{-webkit-transform:none;transform:none}.segment-button-indicator-animated{-webkit-transition:none;transition:none}}:host{--background:none;--background-checked:none;--background-hover:var(--color-checked);--background-focused:var(--color-checked);--background-activated-opacity:0;--background-focused-opacity:.12;--background-hover-opacity:.04;--color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--color-checked:var(--ion-color-primary, #0054e9);--indicator-box-shadow:none;--indicator-color:var(--color-checked);--indicator-height:2px;--indicator-transition:transform 250ms cubic-bezier(0.4, 0, 0.2, 1);--indicator-transform:none;--padding-top:0;--padding-end:16px;--padding-bottom:0;--padding-start:16px;--transition:color 0.15s linear 0s, opacity 0.15s linear 0s;min-width:90px;min-height:48px;border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);font-size:14px;font-weight:500;letter-spacing:0.06em;line-height:40px;text-transform:uppercase}:host(.segment-button-disabled){opacity:0.3}:host(.in-segment-color){background:none;color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color) ion-ripple-effect{color:var(--ion-color-base)}:host(.in-segment-color) .segment-button-indicator-background{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked) .button-native{color:var(--ion-color-base)}:host(.in-segment-color.ion-focused) .button-native::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.in-segment-color:hover) .button-native{color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6)}:host(.in-segment-color:hover) .button-native::after{background:var(--ion-color-base)}:host(.in-segment-color.segment-button-checked:hover) .button-native{color:var(--ion-color-base)}}:host(.in-toolbar:not(.in-segment-color)){--background:var(--ion-toolbar-segment-background, none);--background-checked:var(--ion-toolbar-segment-background-checked, none);--color:var(--ion-toolbar-segment-color, rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.6));--color-checked:var(--ion-toolbar-segment-color-checked, var(--ion-color-primary, #0054e9));--indicator-color:var(--ion-toolbar-segment-color-checked, var(--color-checked))}:host(.in-toolbar-color:not(.in-segment-color)) .button-native{color:rgba(var(--ion-color-contrast-rgb), 0.6)}:host(.in-toolbar-color.segment-button-checked:not(.in-segment-color)) .button-native{color:var(--ion-color-contrast)}@media (any-hover: hover){:host(.in-toolbar-color:not(.in-segment-color)) .button-native::after{background:var(--ion-color-contrast)}}::slotted(ion-icon){margin-top:12px;margin-bottom:12px;font-size:24px}::slotted(ion-label){margin-top:12px;margin-bottom:12px}:host(.segment-button-layout-icon-top) ::slotted(ion-label),:host(.segment-button-layout-icon-bottom) ::slotted(ion-icon){margin-top:0}:host(.segment-button-layout-icon-top) ::slotted(ion-icon),:host(.segment-button-layout-icon-bottom) ::slotted(ion-label){margin-bottom:0}:host(.segment-button-layout-icon-start) ::slotted(ion-label){-webkit-margin-start:8px;margin-inline-start:8px;-webkit-margin-end:0;margin-inline-end:0}:host(.segment-button-layout-icon-end) ::slotted(ion-label){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px}:host(.segment-button-has-icon-only) ::slotted(ion-icon){margin-top:12px;margin-bottom:12px}:host(.segment-button-has-label-only) ::slotted(ion-label){margin-top:12px;margin-bottom:12px}.segment-button-indicator{left:0;right:0;bottom:0}.segment-button-indicator-background{background:var(--indicator-color)}:host(.in-toolbar:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-toolbar-segment-indicator-color, var(--indicator-color))}:host(.in-toolbar-color:not(.in-segment-color)) .segment-button-indicator-background{background:var(--ion-color-contrast)}'},m})()},4576:(V,w,h)=>{h.d(w,{c:()=>f,g:()=>v,h:()=>r,o:()=>_});var y=h(467);const r=(c,l)=>null!==l.closest(c),f=(c,l)=>"string"==typeof c&&c.length>0?Object.assign({"ion-color":!0,[`ion-color-${c}`]:!0},l):l,v=c=>{const l={};return(c=>void 0!==c?(Array.isArray(c)?c:c.split(" ")).filter(u=>null!=u).map(u=>u.trim()).filter(u=>""!==u):[])(c).forEach(u=>l[u]=!0),l},B=/^[a-z][a-z0-9+\-.]*:/,_=function(){var c=(0,y.A)(function*(l,u,S,E){if(null!=l&&"#"!==l[0]&&!B.test(l)){const m=document.querySelector("ion-router");if(m)return u?.preventDefault(),m.push(l,S,E)}return!1});return function(u,S,E,m){return c.apply(this,arguments)}}()}}]);