"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[7676],{4576:(E,m,d)=>{d.d(m,{c:()=>x,g:()=>k,h:()=>p,o:()=>I});var y=d(467);const p=(l,u)=>null!==u.closest(l),x=(l,u)=>"string"==typeof l&&l.length>0?Object.assign({"ion-color":!0,[`ion-color-${l}`]:!0},u):u,k=l=>{const u={};return(l=>void 0!==l?(Array.isArray(l)?l:l.split(" ")).filter(o=>null!=o).map(o=>o.trim()).filter(o=>""!==o):[])(l).forEach(o=>u[o]=!0),u},w=/^[a-z][a-z0-9+\-.]*:/,I=function(){var l=(0,y.A)(function*(u,o,i,t){if(null!=u&&"#"!==u[0]&&!w.test(u)){const n=document.querySelector("ion-router");if(n)return o?.preventDefault(),n.push(u,i,t)}return!1});return function(o,i,t,n){return l.apply(this,arguments)}}()},7676:(E,m,d)=>{d.r(m),d.d(m,{ion_input_otp:()=>l});var y=d(467),p=d(2734),x=d(1837),_=d(647),k=d(4576);const l=class{constructor(o){(0,p.r)(this,o),this.ionInput=(0,p.d)(this,"ionInput",7),this.ionChange=(0,p.d)(this,"ionChange",7),this.ionComplete=(0,p.d)(this,"ionComplete",7),this.ionBlur=(0,p.d)(this,"ionBlur",7),this.ionFocus=(0,p.d)(this,"ionFocus",7),this.inheritedAttributes={},this.inputRefs=[],this.inputId="ion-input-otp-"+u++,this.parsedSeparators=[],this.isKeyboardNavigation=!1,this.inputValues=[],this.hasFocus=!1,this.autocapitalize="off",this.disabled=!1,this.fill="outline",this.length=4,this.readonly=!1,this.shape="round",this.size="medium",this.type="number",this.value="",this.onFocus=i=>t=>{var n;const{inputRefs:s}=this;this.hasFocus||(this.ionFocus.emit(t),this.focusedValue=this.value),this.hasFocus=!0;let e=i;if(!this.isKeyboardNavigation){const a=this.inputValues[i]?i:this.getFirstEmptyIndex();e=-1===a?this.length-1:a,null===(n=this.inputRefs[e])||void 0===n||n.focus()}s.forEach((a,c)=>{a.tabIndex=c===e?0:-1}),this.isKeyboardNavigation=!1},this.onBlur=i=>{const{inputRefs:t}=this,n=i.relatedTarget;null!=n&&t.includes(n)||(this.hasFocus=!1,this.updateTabIndexes(),this.ionBlur.emit(i),this.focusedValue!==this.value&&this.emitIonChange(i))},this.onKeyDown=i=>t=>{const{length:n}=this,s=(0,_.i)(this.el),e=t.target;if(!(e.selectionStart!==e.selectionEnd||(t.metaKey||t.ctrlKey)&&["a","c","v","x","r","z","y"].includes(t.key.toLowerCase()))){if("Backspace"===t.key)if(this.inputValues[i]){for(let r=i;r<n-1;r++)this.inputValues[r]=this.inputValues[r+1];this.inputValues[n-1]="";for(let r=0;r<n;r++)this.inputRefs[r].value=this.inputValues[r]||"";this.updateValue(t),t.preventDefault()}else!this.inputValues[i]&&i>0&&this.focusPrevious(i);else if("ArrowLeft"===t.key||"ArrowRight"===t.key){this.isKeyboardNavigation=!0,t.preventDefault();const r="ArrowLeft"===t.key;r&&s||!r&&!s?this.inputValues[i]&&i<n-1&&this.focusNext(i):this.focusPrevious(i)}else if("Tab"===t.key)return void(this.isKeyboardNavigation=!0);if(this.inputValues[i]&&this.validKeyPattern.test(t.key)){if(!this.inputValues[n-1])for(let r=n-1;r>i;r--)this.inputValues[r]=this.inputValues[r-1],this.inputRefs[r].value=this.inputValues[r]||"";this.inputValues[i]=t.key,this.inputRefs[i].value=t.key,this.updateValue(t),t.preventDefault()}}},this.onInput=i=>t=>{const{length:n,validKeyPattern:s}=this,e=t.target.value;if(e.length>1){const a=e.split("").filter(c=>s.test(c)).slice(0,n);return 0===a.length&&requestAnimationFrame(()=>{this.inputRefs.forEach(c=>{c.value=""})}),this.value=a.join(""),this.updateValue(t),void setTimeout(()=>{var c;null===(c=this.inputRefs[a.length<n?a.length:n-1])||void 0===c||c.focus()},20)}if(e.length>0&&!s.test(e))return this.inputRefs[i].value="",void(this.inputValues[i]="");this.inputValues[i]=e,this.updateValue(t),e.length>0&&this.focusNext(i)},this.onPaste=i=>{var t,n,s;const{inputRefs:e,length:a,validKeyPattern:c}=this;i.preventDefault();const r=null===(t=i.clipboardData)||void 0===t?void 0:t.getData("text");if(!r)return void this.emitIonInput(i);const g=r.split("").filter(f=>c.test(f)).slice(0,a);g.forEach((f,v)=>{v<a&&(this.inputRefs[v].value=f,this.inputValues[v]=f)}),this.value=g.join(""),this.updateValue(i);const b=g.length;b<a?null===(n=e[b])||void 0===n||n.focus():null===(s=e[a-1])||void 0===s||s.focus()}}setFocus(o){var i=this;return(0,y.A)(function*(){var t,n;if("number"==typeof o){const s=Math.max(0,Math.min(o,i.length-1));null===(t=i.inputRefs[s])||void 0===t||t.focus()}else{const s=i.getTabbableIndex();null===(n=i.inputRefs[s])||void 0===n||n.focus()}})()}valueChanged(){this.initializeValues(),this.updateTabIndexes()}processSeparators(){const{separators:o,length:i}=this;if(void 0===o)return void(this.parsedSeparators=[]);if("string"==typeof o&&"all"!==o&&!/^(\d+)(,\d+)*$/.test(o))return(0,p.m)(`[ion-input-otp] - Invalid separators format. Expected a comma-separated list of numbers, an array of numbers, or "all". Received: ${o}`,this.el),void(this.parsedSeparators=[]);let t;t="all"===o?Array.from({length:i-1},(e,a)=>a+1):Array.isArray(o)?o:o.split(",").map(e=>parseInt(e,10)).filter(e=>!isNaN(e)),t.filter((e,a)=>t.indexOf(e)!==a).length>0&&(0,p.m)(`[ion-input-otp] - Duplicate separator positions are not allowed. Received: ${o}`,this.el);const s=t.filter(e=>e>i);s.length>0&&(0,p.m)(`[ion-input-otp] - The following separator positions are greater than the input length (${i}): ${s.join(", ")}. These separators will be ignored.`,this.el),this.parsedSeparators=t.filter(e=>e<=i)}componentWillLoad(){this.inheritedAttributes=(0,x.i)(this.el),this.processSeparators(),this.initializeValues()}componentDidLoad(){this.updateTabIndexes()}get validKeyPattern(){return new RegExp(`^${this.getPattern()}$`,"u")}getPattern(){const{pattern:o,type:i}=this;return o||("number"===i?"[\\p{N}]":"[\\p{L}\\p{N}]")}getInputmode(){const{inputmode:o}=this;return o||("number"==this.type?"numeric":"text")}initializeValues(){this.inputValues=Array(this.length).fill(""),null!=this.value&&0!==String(this.value).length&&(String(this.value).split("").slice(0,this.length).forEach((i,t)=>{this.validKeyPattern.test(i)&&(this.inputValues[t]=i)}),this.value=this.inputValues.join(""))}updateValue(o){const{inputValues:i,length:t}=this,n=i.join("");this.value=n,this.emitIonInput(o),n.length===t&&this.ionComplete.emit({value:n})}emitIonChange(o){const{value:i}=this,t=null==i?i:i.toString();this.ionChange.emit({value:t,event:o})}emitIonInput(o){const{value:i}=this,t=null==i?i:i.toString();this.ionInput.emit({value:t,event:o})}focusNext(o){var i;const{inputRefs:t,length:n}=this;o<n-1&&(null===(i=t[o+1])||void 0===i||i.focus())}focusPrevious(o){var i;const{inputRefs:t}=this;o>0&&(null===(i=t[o-1])||void 0===i||i.focus())}getFirstEmptyIndex(){var o;const{inputValues:i,length:t}=this;return null!==(o=Array.from({length:t},(s,e)=>i[e]||"").findIndex(s=>!s||""===s))&&void 0!==o?o:-1}getTabbableIndex(){const{length:o}=this,i=this.getFirstEmptyIndex();return-1===i?o-1:i}updateTabIndexes(){const{inputRefs:o,inputValues:i,length:t}=this;let n=-1;for(let s=0;s<t;s++)if(!i[s]||""===i[s]){n=s;break}o.forEach((s,e)=>{const a=-1===n?e===t-1:n===e;s.tabIndex=a?0:-1,s.setAttribute("aria-hidden",i[e]&&""!==i[e]||a?"false":"true")})}showSeparator(o){const{length:i}=this;return this.parsedSeparators.includes(o+1)&&o<i-1}render(){var o,i;const{autocapitalize:t,color:n,disabled:s,el:e,fill:a,hasFocus:c,inheritedAttributes:r,inputId:g,inputRefs:b,inputValues:f,length:v,readonly:V,shape:C,size:z}=this,D=(0,p.e)(this),R=this.getInputmode(),P=this.getTabbableIndex(),T=this.getPattern(),S=""!==(null===(i=null===(o=e.querySelector(".input-otp-description"))||void 0===o?void 0:o.textContent)||void 0===i?void 0:i.trim());return(0,p.h)(p.j,{key:"df8fca036cedea0812185a02e3b655d7d76285e0",class:(0,k.c)(n,{[D]:!0,"has-focus":c,[`input-otp-size-${z}`]:!0,[`input-otp-shape-${C}`]:!0,[`input-otp-fill-${a}`]:!0,"input-otp-disabled":s,"input-otp-readonly":V})},(0,p.h)("div",Object.assign({key:"831be3f939cf037f0eb8d7e37e0afd4ef9a3c2c5",role:"group","aria-label":"One-time password input",class:"input-otp-group"},r),Array.from({length:v}).map((O,h)=>(0,p.h)(p.F,null,(0,p.h)("div",{class:"native-wrapper"},(0,p.h)("input",{class:"native-input",id:`${g}-${h}`,"aria-label":`Input ${h+1} of ${v}`,type:"text",autoCapitalize:t,inputmode:R,pattern:T,disabled:s,readOnly:V,tabIndex:h===P?0:-1,value:f[h]||"",autocomplete:"one-time-code",ref:A=>b[h]=A,onInput:this.onInput(h),onBlur:this.onBlur,onFocus:this.onFocus(h),onKeyDown:this.onKeyDown(h),onPaste:this.onPaste})),this.showSeparator(h)&&(0,p.h)("div",{class:"input-otp-separator"})))),(0,p.h)("div",{key:"5311fedc34f7af3efd5f69e5a3d768055119c4f1",class:{"input-otp-description":!0,"input-otp-description-hidden":!S}},(0,p.h)("slot",{key:"9e8afa2f7fa76c3092582dc27770fdf565a1b9ba"})))}get el(){return(0,p.k)(this)}static get watchers(){return{value:["valueChanged"],separators:["processSeparators"],length:["processSeparators"]}}};let u=0;l.style={ios:".sc-ion-input-otp-ios-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-ios{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-ios{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-ios{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-ios{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-ios{display:none}.input-otp-separator.sc-ion-input-otp-ios{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-ios-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:8px}.input-otp-size-medium.sc-ion-input-otp-ios-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-ios-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios,.input-otp-size-large.sc-ion-input-otp-ios-h .input-otp-group.sc-ion-input-otp-ios{gap:12px}.input-otp-shape-round.sc-ion-input-otp-ios-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-ios-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-ios-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-ios-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-disabled.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-ios-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-ios-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-ios-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-ios-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-ios-h,.ion-touched.ion-invalid.sc-ion-input-otp-ios-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-ios-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-ios-h{--border-width:0.55px}.has-focus.sc-ion-input-otp-ios-h .native-input.sc-ion-input-otp-ios:focus{--border-width:1px}.input-otp-fill-outline.sc-ion-input-otp-ios-h{--border-color:var(--ion-item-border-color, var(--ion-border-color, var(--ion-color-step-250, var(--ion-background-color-step-250, #c8c7cc))))}",md:".sc-ion-input-otp-md-h{--margin-top:0;--margin-end:0;--margin-bottom:0;--margin-start:0;--padding-top:16px;--padding-end:0;--padding-bottom:16px;--padding-start:0;--color:initial;--min-width:40px;--separator-width:8px;--separator-height:var(--separator-width);--separator-border-radius:999px;--separator-color:var(--ion-color-step-150, var(--ion-background-color-step-150, #d9d9d9));--highlight-color-focused:var(--ion-color-primary, #0054e9);--highlight-color-valid:var(--ion-color-success, #2dd55b);--highlight-color-invalid:var(--ion-color-danger, #c5000f);--highlight-color:var(--highlight-color-focused);display:block;position:relative;font-size:0.875rem}.input-otp-group.sc-ion-input-otp-md{-webkit-margin-start:var(--margin-start);margin-inline-start:var(--margin-start);-webkit-margin-end:var(--margin-end);margin-inline-end:var(--margin-end);margin-top:var(--margin-top);margin-bottom:var(--margin-bottom);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center}.native-wrapper.sc-ion-input-otp-md{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;min-width:var(--min-width)}.native-input.sc-ion-input-otp-md{border-radius:var(--border-radius);width:var(--width);min-width:inherit;height:var(--height);border-width:var(--border-width);border-style:solid;border-color:var(--border-color);background:var(--background);color:var(--color);font-size:inherit;text-align:center;-webkit-appearance:none;-moz-appearance:none;appearance:none}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{caret-color:var(--highlight-color)}.input-otp-description.sc-ion-input-otp-md{color:var(--ion-color-step-700, var(--ion-text-color-step-300, #4d4d4d));font-size:0.75rem;line-height:1.25rem;text-align:center}.input-otp-description-hidden.sc-ion-input-otp-md{display:none}.input-otp-separator.sc-ion-input-otp-md{border-radius:var(--separator-border-radius);-ms-flex-negative:0;flex-shrink:0;width:var(--separator-width);height:var(--separator-height);background:var(--separator-color)}.input-otp-size-small.sc-ion-input-otp-md-h{--width:40px;--height:40px}.input-otp-size-small.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:8px}.input-otp-size-medium.sc-ion-input-otp-md-h{--width:48px;--height:48px}.input-otp-size-large.sc-ion-input-otp-md-h{--width:56px;--height:56px}.input-otp-size-medium.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md,.input-otp-size-large.sc-ion-input-otp-md-h .input-otp-group.sc-ion-input-otp-md{gap:12px}.input-otp-shape-round.sc-ion-input-otp-md-h{--border-radius:16px}.input-otp-shape-soft.sc-ion-input-otp-md-h{--border-radius:8px}.input-otp-shape-rectangular.sc-ion-input-otp-md-h{--border-radius:0}.input-otp-fill-outline.sc-ion-input-otp-md-h{--background:none}.input-otp-fill-solid.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-disabled.sc-ion-input-otp-md-h{--color:var(--ion-color-step-350, var(--ion-text-color-step-650, #a6a6a6))}.input-otp-fill-outline.input-otp-disabled.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2));--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-disabled.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:disabled{cursor:not-allowed}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-color:var(--highlight-color);outline:none}.input-otp-fill-outline.input-otp-readonly.sc-ion-input-otp-md-h{--background:var(--ion-color-step-50, var(--ion-background-color-step-50, #f2f2f2))}.input-otp-fill-solid.input-otp-disabled.sc-ion-input-otp-md-h,.input-otp-fill-solid.input-otp-readonly.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6));--background:var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-invalid)}.ion-valid.sc-ion-input-otp-md-h{--highlight-color:var(--highlight-color-valid)}.has-focus.ion-valid.sc-ion-input-otp-md-h,.ion-touched.ion-invalid.sc-ion-input-otp-md-h{--border-color:var(--highlight-color)}.ion-color.sc-ion-input-otp-md-h{--highlight-color-focused:var(--ion-color-base)}.input-otp-fill-outline.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{border-color:rgba(var(--ion-color-base-rgb), 0.6)}.input-otp-fill-outline.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-invalid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-danger, #c5000f)}.input-otp-fill-outline.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-outline.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md,.input-otp-fill-solid.ion-color.has-focus.ion-valid.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:var(--ion-color-success, #2dd55b)}.input-otp-fill-outline.input-otp-disabled.ion-color.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md{border-color:rgba(var(--ion-color-base-rgb), 0.3)}.sc-ion-input-otp-md-h{--border-width:1px}.has-focus.sc-ion-input-otp-md-h .native-input.sc-ion-input-otp-md:focus{--border-width:2px}.input-otp-fill-outline.sc-ion-input-otp-md-h{--border-color:var(--ion-color-step-300, var(--ion-background-color-step-300, #b3b3b3))}"}}}]);