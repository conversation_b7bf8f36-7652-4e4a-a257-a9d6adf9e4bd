"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[9073],{9073:(c,s,n)=>{n.r(s),n.d(s,{ion_segment_content:()=>l});var e=n(2734);const l=(()=>{let t=class{constructor(o){(0,e.r)(this,o)}render(){return(0,e.h)(e.j,{key:"db6876f2aee7afa1ea8bc147337670faa68fae1c"},(0,e.h)("slot",{key:"bc05714a973a5655668679033f5809a1da6db8cc"}))}};return t.style=":host{scroll-snap-align:center;scroll-snap-stop:always;-ms-flex-negative:0;flex-shrink:0;width:100%;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none;}:host::-webkit-scrollbar{display:none}",t})()}}]);