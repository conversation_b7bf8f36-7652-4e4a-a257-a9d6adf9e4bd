"use strict";(self.webpackChunkapp=self.webpackChunkapp||[]).push([[441],{441:(xe,X,y)=>{y.r(X),y.d(X,{ion_modal:()=>be});var L=y(467),l=y(2734),A=y(4657),z=y(1653),b=y(1837),oe=y(7930),re=y(6780),c=y(3217),_=y(4576),B=y(4590),q=y(3224),u=y(5756),$=y(9166),ce=y(6011),Q=y(9596),F=(y(1906),y(8607),y(9043),function(e){return e.Dark="DARK",e.Light="LIGHT",e.Default="DEFAULT",e}(F||{}));const ie={getEngine(){const e=(0,re.g)();if(e?.isPluginAvailable("StatusBar"))return e.Plugins.StatusBar},setStyle(e){const t=this.getEngine();t&&t.setStyle(e)},getStyle:(e=(0,L.A)(function*(){const t=this.getEngine();if(!t)return F.Default;const{style:n}=yield t.getInfo();return n}),function(){return e.apply(this,arguments)})},se=(e,t)=>{if(1===t)return 0;const n=1/(1-t);return e*n+-t*n},pe=()=>{!Q.w||Q.w.innerWidth>=768||ie.setStyle({style:F.Dark})},ae=(e=F.Default)=>{!Q.w||Q.w.innerWidth>=768||ie.setStyle({style:e})},he=function(){var e=(0,L.A)(function*(t,n){"function"!=typeof t.canDismiss||!(yield t.canDismiss(void 0,c.G))||(n.isRunning()?n.onFinish(()=>{t.dismiss(void 0,"handler")},{oneTimeCallback:!0}):t.dismiss(void 0,"handler"))});return function(n,o){return e.apply(this,arguments)}}(),de=e=>.00255275*2.71828**(-14.9619*e)-1.00255*2.71828**(-.0380968*e)+1,fe=(e,t)=>(0,b.e)(400,e/Math.abs(1.1*t),500),me=e=>{const{currentBreakpoint:t,backdropBreakpoint:n,expandToScroll:o}=e,s=void 0===n||n<t,i=s?`calc(var(--backdrop-opacity) * ${t})`:"0",a=(0,u.c)("backdropAnimation").fromTo("opacity",0,i);return s&&a.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),{wrapperAnimation:(0,u.c)("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-100*t}%)`}]),backdropAnimation:a,contentAnimation:o?void 0:(0,u.c)("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:100*(1-t)+"%"},{offset:1,opacity:1,maxHeight:100*t+"%"}])}},ue=e=>{const{currentBreakpoint:t,backdropBreakpoint:n}=e,o=`calc(var(--backdrop-opacity) * ${se(t,n)})`,s=[{offset:0,opacity:o},{offset:1,opacity:0}],i=[{offset:0,opacity:o},{offset:n,opacity:0},{offset:1,opacity:0}],a=(0,u.c)("backdropAnimation").keyframes(0!==n?i:s);return{wrapperAnimation:(0,u.c)("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-100*t}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:a}},ge=(e,t)=>{const{presentingEl:n,currentBreakpoint:o,expandToScroll:s}=t,i=(0,b.g)(e),{wrapperAnimation:a,backdropAnimation:r,contentAnimation:d}=void 0!==o?me(t):{backdropAnimation:(0,u.c)().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),wrapperAnimation:(0,u.c)().fromTo("transform","translateY(100vh)","translateY(0vh)"),contentAnimation:void 0};r.addElement(i.querySelector("ion-backdrop")),a.addElement(i.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!s&&d?.addElement(e.querySelector(".ion-page"));const v=(0,u.c)("entering-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([a]);if(d&&v.addAnimation(d),n){const T=window.innerWidth<768,M="ION-MODAL"===n.tagName&&void 0!==n.presentingElement,x=(0,b.g)(n),S=(0,u.c)().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),w=document.body;if(T){const O=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",j=`translateY(${M?"-10px":O}) scale(0.915)`;S.afterStyles({transform:j}).beforeAddWrite(()=>w.style.setProperty("background-color","black")).addElement(n).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:j,borderRadius:"10px 10px 0 0"}]),v.addAnimation(S)}else if(v.addAnimation(r),M){const h=`translateY(-10px) scale(${M?.915:1})`;S.afterStyles({transform:h}).addElement(x.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:h}]);const Y=(0,u.c)().afterStyles({transform:h}).addElement(x.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:h}]);v.addAnimation([S,Y])}else a.fromTo("opacity","0","1")}else v.addAnimation(r);return v},ye=(e,t,n=500)=>{const{presentingEl:o,currentBreakpoint:s}=t,i=(0,b.g)(e),{wrapperAnimation:a,backdropAnimation:r}=void 0!==s?ue(t):{backdropAnimation:(0,u.c)().fromTo("opacity","var(--backdrop-opacity)",0),wrapperAnimation:(0,u.c)().fromTo("transform","translateY(0vh)","translateY(100vh)")};r.addElement(i.querySelector("ion-backdrop")),a.addElement(i.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});const d=(0,u.c)("leaving-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(n).addAnimation(a);if(o){const v=window.innerWidth<768,T="ION-MODAL"===o.tagName&&void 0!==o.presentingElement,M=(0,b.g)(o),x=(0,u.c)().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(w=>{1===w&&(o.style.setProperty("overflow",""),Array.from(S.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(h=>void 0!==h.presentingElement).length<=1&&S.style.setProperty("background-color",""))}),S=document.body;if(v){const w=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",Y=`translateY(${T?"-10px":w}) scale(0.915)`;x.addElement(o).keyframes([{offset:0,filter:"contrast(0.85)",transform:Y,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),d.addAnimation(x)}else if(d.addAnimation(r),T){const O=`translateY(-10px) scale(${T?.915:1})`;x.addElement(M.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:O},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);const h=(0,u.c)().addElement(M.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:O},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);d.addAnimation([x,h])}else a.fromTo("opacity","1","0")}else d.addAnimation(r);return d},Ae=(e,t)=>{const{currentBreakpoint:n,expandToScroll:o}=t,s=(0,b.g)(e),{wrapperAnimation:i,backdropAnimation:a,contentAnimation:r}=void 0!==n?me(t):{backdropAnimation:(0,u.c)().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),wrapperAnimation:(0,u.c)().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]),contentAnimation:void 0};a.addElement(s.querySelector("ion-backdrop")),i.addElement(s.querySelector(".modal-wrapper")),!o&&r?.addElement(e.querySelector(".ion-page"));const d=(0,u.c)().addElement(e).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([a,i]);return r&&d.addAnimation(r),d},Ce=(e,t)=>{const{currentBreakpoint:n}=t,o=(0,b.g)(e),{wrapperAnimation:s,backdropAnimation:i}=void 0!==n?ue(t):{backdropAnimation:(0,u.c)().fromTo("opacity","var(--backdrop-opacity)",0),wrapperAnimation:(0,u.c)().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}])};return i.addElement(o.querySelector("ion-backdrop")),s.addElement(o.querySelector(".modal-wrapper")),(0,u.c)().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([i,s])},be=class{constructor(e){(0,l.r)(this,e),this.didPresent=(0,l.d)(this,"ionModalDidPresent",7),this.willPresent=(0,l.d)(this,"ionModalWillPresent",7),this.willDismiss=(0,l.d)(this,"ionModalWillDismiss",7),this.didDismiss=(0,l.d)(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=(0,l.d)(this,"ionBreakpointDidChange",7),this.didPresentShorthand=(0,l.d)(this,"didPresent",7),this.willPresentShorthand=(0,l.d)(this,"willPresent",7),this.willDismissShorthand=(0,l.d)(this,"willDismiss",7),this.didDismissShorthand=(0,l.d)(this,"didDismiss",7),this.ionMount=(0,l.d)(this,"ionMount",7),this.lockController=(0,oe.c)(),this.triggerController=(0,c.e)(),this.coreDelegate=(0,z.C)(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.expandToScroll=!0,this.backdropBreakpoint=0,this.handleBehavior="none",this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.isOpen=!1,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0,this.onHandleClick=()=>{const{sheetTransition:t,handleBehavior:n}=this;"cycle"!==n||void 0!==t||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{const{sheetTransition:t}=this;void 0===t&&this.dismiss(void 0,c.B)},this.onLifecycle=t=>{const n=this.usersElement,o=Te[t.type];if(n&&o){const s=new CustomEvent(o,{bubbles:!1,cancelable:!1,detail:t.detail});n.dispatchEvent(s)}}}onIsOpenChange(e,t){!0===e&&!1===t?this.present():!1===e&&!0===t&&this.dismiss()}triggerChanged(){const{trigger:e,el:t,triggerController:n}=this;e&&n.addClickListener(t,e)}breakpointsChanged(e){void 0!==e&&(this.sortedBreakpoints=e.sort((t,n)=>t-n))}connectedCallback(){const{el:e}=this;(0,c.j)(e),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var e;const{breakpoints:t,initialBreakpoint:n,el:o,htmlAttributes:s}=this,i=this.isSheetModal=void 0!==t&&void 0!==n,a=["aria-label","role"];this.inheritedAttributes=(0,b.b)(o,a),void 0!==s&&a.forEach(r=>{s[r]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[r]:s[r]}),delete s[r])}),i&&(this.currentBreakpoint=this.initialBreakpoint),void 0!==t&&void 0!==n&&!t.includes(n)&&(0,l.m)("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),null!==(e=this.htmlAttributes)&&void 0!==e&&e.id||(0,c.k)(this.el)}componentDidLoad(){!0===this.isOpen&&(0,b.r)(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};const n=this.inline=null!==this.el.parentNode&&!this.hasController;return{inline:n,delegate:this.workingDelegate=n?this.delegate||this.coreDelegate:this.delegate}}checkCanDismiss(e,t){var n=this;return(0,L.A)(function*(){const{canDismiss:o}=n;return"function"==typeof o?o(e,t):o})()}present(){var e=this;return(0,L.A)(function*(){const t=yield e.lockController.lock();if(e.presented)return void t();const{presentingElement:n,el:o}=e;e.currentBreakpoint=e.initialBreakpoint;const{inline:s,delegate:i}=e.getDelegate(!0);e.ionMount.emit(),e.usersElement=yield(0,z.a)(i,o,e.component,["ion-page"],e.componentProps,s),(0,b.h)(o)?yield(0,B.e)(e.usersElement):e.keepContentsMounted||(yield(0,B.w)()),(0,l.w)(()=>e.el.classList.add("show-modal"));const a=void 0!==n;a&&"ios"===(0,l.e)(e)&&(e.statusBarStyle=yield ie.getStyle(),pe()),yield(0,c.f)(e,"modalEnter",ge,Ae,{presentingEl:n,currentBreakpoint:e.initialBreakpoint,backdropBreakpoint:e.backdropBreakpoint,expandToScroll:e.expandToScroll}),typeof window<"u"&&(e.keyboardOpenCallback=()=>{e.gesture&&(e.gesture.enable(!1),(0,b.r)(()=>{e.gesture&&e.gesture.enable(!0)}))},window.addEventListener(q.KEYBOARD_DID_OPEN,e.keyboardOpenCallback)),e.isSheetModal?e.initSheetGesture():a&&e.initSwipeToClose(),t()})()}initSwipeToClose(){var t,e=this;if("ios"!==(0,l.e)(this))return;const{el:n}=this,o=this.leaveAnimation||l.l.get("modalLeave",ye),s=this.animation=o(n,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!(0,A.a)(n))return void(0,A.p)(n);const a=null!==(t=this.statusBarStyle)&&void 0!==t?t:F.Default;this.gesture=((e,t,n,o)=>{const i=e.offsetHeight;let a=!1,r=!1,d=null,v=null,M=!0,x=0;const j=(0,ce.createGesture)({el:e,gestureName:"modalSwipeToClose",gesturePriority:c.O,direction:"y",threshold:10,canStart:D=>{const k=D.event.target;return null===k||!k.closest||(d=(0,A.f)(k),d?(v=(0,A.i)(d)?(0,b.g)(d).querySelector(".inner-scroll"):d,!d.querySelector("ion-refresher")&&0===v.scrollTop):null===k.closest("ion-footer"))},onStart:D=>{const{deltaY:k}=D;M=!d||!(0,A.i)(d)||d.scrollY,r=void 0!==e.canDismiss&&!0!==e.canDismiss,k>0&&d&&(0,A.d)(d),t.progressStart(!0,a?1:0)},onMove:D=>{const{deltaY:k}=D;k>0&&d&&(0,A.d)(d);const C=D.deltaY/i,R=C>=0&&r,H=R?.2:.9999,V=R?de(C/H):C,W=(0,b.e)(1e-4,V,H);t.progressStep(W),W>=.5&&x<.5?ae(n):W<.5&&x>=.5&&pe(),x=W},onEnd:D=>{const k=D.velocityY,C=D.deltaY/i,R=C>=0&&r,H=R?.2:.9999,V=R?de(C/H):C,W=(0,b.e)(1e-4,V,H),P=!R&&(D.deltaY+1e3*k)/i>=.5;let I=P?-.001:.001;P?(t.easing("cubic-bezier(0.32, 0.72, 0, 1)"),I+=(0,$.g)([0,0],[.32,.72],[0,1],[1,1],W)[0]):(t.easing("cubic-bezier(1, 0, 0.68, 0.28)"),I+=(0,$.g)([0,0],[1,0],[.68,.28],[1,1],W)[0]);const te=fe(P?C*i:(1-W)*i,k);a=P,j.enable(!1),d&&(0,A.r)(d,M),t.onFinish(()=>{P||j.enable(!0)}).progressEnd(P?1:0,I,te),R&&W>H/4?he(e,t):P&&o()}});return j})(n,s,a,()=>{this.gestureAnimationDismissing=!0,ae(this.statusBarStyle),this.animation.onFinish((0,L.A)(function*(){yield e.dismiss(void 0,c.G),e.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){const{wrapperEl:e,initialBreakpoint:t,backdropBreakpoint:n}=this;if(!e||void 0===t)return;const o=this.enterAnimation||l.l.get("modalEnter",ge),s=this.animation=o(this.el,{presentingEl:this.presentingElement,currentBreakpoint:t,backdropBreakpoint:n,expandToScroll:this.expandToScroll});s.progressStart(!0,1);const{gesture:i,moveSheetToBreakpoint:a}=((e,t,n,o,s,i,a=[],r,d,v,T)=>{const S={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:0!==s?[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-s,opacity:0},{offset:1,opacity:0}]:[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},w=e.querySelector("ion-content"),O=n.clientHeight;let h=o,Y=0,j=!1,D=null,k=null,C=null,R=null;const V=a[a.length-1],W=a[0],N=i.childAnimations.find(m=>"wrapperAnimation"===m.id),P=i.childAnimations.find(m=>"backdropAnimation"===m.id),I=i.childAnimations.find(m=>"contentAnimation"===m.id),te=()=>{e.style.setProperty("pointer-events","auto"),t.style.setProperty("pointer-events","auto"),e.classList.remove(c.F)},ke=()=>{e.style.setProperty("pointer-events","none"),t.style.setProperty("pointer-events","none"),e.classList.add(c.F)},G=m=>{if(!k&&(k=Array.from(e.querySelectorAll("ion-footer")),!k.length))return;const p=e.querySelector(".ion-page");if(R=m,"stationary"===m)k.forEach(g=>{g.classList.remove("modal-footer-moving"),g.style.removeProperty("position"),g.style.removeProperty("width"),g.style.removeProperty("height"),g.style.removeProperty("top"),g.style.removeProperty("left"),p?.style.removeProperty("padding-bottom"),p?.appendChild(g)});else{let g=0;k.forEach((f,U)=>{const K=f.getBoundingClientRect(),E=document.body.getBoundingClientRect();g+=f.clientHeight;const Z=K.top-E.top,J=K.left-E.left;if(f.style.setProperty("--pinned-width",`${f.clientWidth}px`),f.style.setProperty("--pinned-height",`${f.clientHeight}px`),f.style.setProperty("--pinned-top",`${Z}px`),f.style.setProperty("--pinned-left",`${J}px`),0===U){C=Z;const le=e.querySelector("ion-header");le&&(C-=le.clientHeight)}}),k.forEach(f=>{p?.style.setProperty("padding-bottom",`${g}px`),f.classList.add("modal-footer-moving"),f.style.setProperty("position","absolute"),f.style.setProperty("width","var(--pinned-width)"),f.style.setProperty("height","var(--pinned-height)"),f.style.setProperty("top","var(--pinned-top)"),f.style.setProperty("left","var(--pinned-left)"),document.body.appendChild(f)})}};N&&P&&(N.keyframes([...S.WRAPPER_KEYFRAMES]),P.keyframes([...S.BACKDROP_KEYFRAMES]),I?.keyframes([...S.CONTENT_KEYFRAMES]),i.progressStart(!0,1-h),h>s?te():ke()),w&&h!==V&&r&&(w.scrollY=!1);const ve=m=>{const{breakpoint:p,canDismiss:g,breakpointOffset:f,animated:U}=m,K=g&&0===p,E=K?h:p,Z=0!==E;return h=0,N&&P&&(N.keyframes([{offset:0,transform:`translateY(${100*f}%)`},{offset:1,transform:`translateY(${100*(1-E)}%)`}]),P.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${se(1-f,s)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${se(E,s)})`}]),I&&I.keyframes([{offset:0,maxHeight:100*(1-f)+"%"},{offset:1,maxHeight:100*E+"%"}]),i.progressStep(0)),ne.enable(!1),K?he(e,i):Z||v(),w&&(E===a[a.length-1]||!r)&&(w.scrollY=!0),!r&&0===E&&G("stationary"),new Promise(J=>{i.onFinish(()=>{Z?(r||G("stationary"),N&&P?(0,b.r)(()=>{N.keyframes([...S.WRAPPER_KEYFRAMES]),P.keyframes([...S.BACKDROP_KEYFRAMES]),I?.keyframes([...S.CONTENT_KEYFRAMES]),i.progressStart(!0,1-E),h=E,T(h),h>s?te():ke(),ne.enable(!0),J()}):(ne.enable(!0),J())):J()},{oneTimeCallback:!0}).progressEnd(1,0,U?500:0)})},ne=(0,ce.createGesture)({el:n,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:m=>{const p=(0,A.f)(m.event.target);if(h=d(),!r&&p)return 0===((0,A.i)(p)?(0,b.g)(p).querySelector(".inner-scroll"):p).scrollTop;if(1===h&&p){const g=(0,A.i)(p)?(0,b.g)(p).querySelector(".inner-scroll"):p;return!p.querySelector("ion-refresher")&&0===g.scrollTop}return!0},onStart:m=>{if(j=void 0!==e.canDismiss&&!0!==e.canDismiss&&0===W,!r){const p=(0,A.f)(m.event.target);D=p&&(0,A.i)(p)?(0,b.g)(p).querySelector(".inner-scroll"):p}r||G("moving"),m.deltaY>0&&w&&(w.scrollY=!1),(0,b.r)(()=>{e.focus()}),i.progressStart(!0,1-h)},onMove:m=>{if(!r&&null!==C&&null!==R&&(m.currentY>=C&&"moving"===R?G("stationary"):m.currentY<C&&"stationary"===R&&G("moving")),!r&&m.deltaY<=0&&D)return;m.deltaY>0&&w&&(w.scrollY=!1);const g=a.length>1?1-a[1]:void 0,f=1-h+m.deltaY/O,U=void 0!==g&&f>=g&&j,K=U?.95:.9999,E=U&&void 0!==g?g+de((f-g)/(K-g)):f;Y=(0,b.e)(1e-4,E,K),i.progressStep(Y)},onEnd:m=>{if(!r&&m.deltaY<=0&&D&&D.scrollTop>0)return void G("stationary");const f=h-(m.deltaY+350*m.velocityY)/O,U=a.reduce((K,E)=>Math.abs(E-f)<Math.abs(K-f)?E:K);ve({breakpoint:U,breakpointOffset:Y,canDismiss:j,animated:!0})}});return{gesture:ne,moveSheetToBreakpoint:ve}})(this.el,this.backdropEl,e,t,n,s,this.sortedBreakpoints,this.expandToScroll,()=>{var r;return null!==(r=this.currentBreakpoint)&&void 0!==r?r:0},()=>this.sheetOnDismiss(),r=>{this.currentBreakpoint!==r&&(this.currentBreakpoint=r,this.ionBreakpointDidChange.emit({breakpoint:r}))});this.gesture=i,this.moveSheetToBreakpoint=a,this.gesture.enable(!0)}sheetOnDismiss(){var e=this;this.gestureAnimationDismissing=!0,this.animation.onFinish((0,L.A)(function*(){e.currentBreakpoint=0,e.ionBreakpointDidChange.emit({breakpoint:e.currentBreakpoint}),yield e.dismiss(void 0,c.G),e.gestureAnimationDismissing=!1}))}dismiss(e,t){var n=this;return(0,L.A)(function*(){var o;if(n.gestureAnimationDismissing&&t!==c.G)return!1;const s=yield n.lockController.lock();if("handler"!==t&&!(yield n.checkCanDismiss(e,t)))return s(),!1;const{presentingElement:i}=n;void 0!==i&&"ios"===(0,l.e)(n)&&ae(n.statusBarStyle),typeof window<"u"&&n.keyboardOpenCallback&&(window.removeEventListener(q.KEYBOARD_DID_OPEN,n.keyboardOpenCallback),n.keyboardOpenCallback=void 0);const r=yield(0,c.g)(n,e,t,"modalLeave",ye,Ce,{presentingEl:i,currentBreakpoint:null!==(o=n.currentBreakpoint)&&void 0!==o?o:n.initialBreakpoint,backdropBreakpoint:n.backdropBreakpoint,expandToScroll:n.expandToScroll});if(r){const{delegate:d}=n.getDelegate();yield(0,z.d)(d,n.usersElement),(0,l.w)(()=>n.el.classList.remove("show-modal")),n.animation&&n.animation.destroy(),n.gesture&&n.gesture.destroy()}return n.currentBreakpoint=void 0,n.animation=void 0,s(),r})()}onDidDismiss(){return(0,c.h)(this.el,"ionModalDidDismiss")}onWillDismiss(){return(0,c.h)(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(e){var t=this;return(0,L.A)(function*(){if(!t.isSheetModal)return void(0,l.m)("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");if(!t.breakpoints.includes(e))return void(0,l.m)(`[ion-modal] - Attempted to set invalid breakpoint value ${e}. Please double check that the breakpoint value is part of your defined breakpoints.`);const{currentBreakpoint:n,moveSheetToBreakpoint:o,canDismiss:s,breakpoints:i,animated:a}=t;n!==e&&o&&(t.sheetTransition=o({breakpoint:e,breakpointOffset:1-n,canDismiss:void 0!==s&&!0!==s&&0===i[0],animated:a}),yield t.sheetTransition,t.sheetTransition=void 0)})()}getCurrentBreakpoint(){var e=this;return(0,L.A)(function*(){return e.currentBreakpoint})()}moveToNextBreakpoint(){var e=this;return(0,L.A)(function*(){const{breakpoints:t,currentBreakpoint:n}=e;if(!t||null==n)return!1;const o=t.filter(r=>0!==r),i=(o.indexOf(n)+1)%o.length,a=o[i];return yield e.setCurrentBreakpoint(a),!0})()}render(){const{handle:e,isSheetModal:t,presentingElement:n,htmlAttributes:o,handleBehavior:s,inheritedAttributes:i,focusTrap:a,expandToScroll:r}=this,d=!1!==e&&t,v=(0,l.e)(this),T=void 0!==n&&"ios"===v,M="cycle"===s;return(0,l.h)(l.j,Object.assign({key:"0bcbdcfcd7d890eb599da3f97f21c317d34f8e0e","no-router":!0,tabindex:"-1"},o,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[v]:!0,"modal-default":!T&&!t,"modal-card":T,"modal-sheet":t,"modal-no-expand-scroll":t&&!r,"overlay-hidden":!0,[c.F]:!1===a},(0,_.g)(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle}),(0,l.h)("ion-backdrop",{key:"d72159e73daa5af7349aa9e8f695aa435eb43069",ref:x=>this.backdropEl=x,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),"ios"===v&&(0,l.h)("div",{key:"fd2d9b13676ae72473881649a397b6eacde03a03",class:"modal-shadow"}),(0,l.h)("div",Object.assign({key:"908eccb1ad982dcde2dbcff0cbb18b6e60f8ba74",role:"dialog"},i,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:x=>this.wrapperEl=x}),d&&(0,l.h)("button",{key:"332dc0b40363a77c7be62331d9f26def91c790e9",class:"modal-handle",tabIndex:M?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:M?this.onHandleClick:void 0,part:"handle"}),(0,l.h)("slot",{key:"c32698350193c450327e97049daf8b8d1fda0d0e"})))}get el(){return(0,l.k)(this)}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}},Te={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};var e;be.style={ios:':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}',md:':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}'}},4576:(xe,X,y)=>{y.d(X,{c:()=>A,g:()=>b,h:()=>l,o:()=>re});var L=y(467);const l=(c,_)=>null!==_.closest(c),A=(c,_)=>"string"==typeof c&&c.length>0?Object.assign({"ion-color":!0,[`ion-color-${c}`]:!0},_):_,b=c=>{const _={};return(c=>void 0!==c?(Array.isArray(c)?c:c.split(" ")).filter(B=>null!=B).map(B=>B.trim()).filter(B=>""!==B):[])(c).forEach(B=>_[B]=!0),_},oe=/^[a-z][a-z0-9+\-.]*:/,re=function(){var c=(0,L.A)(function*(_,B,q,u){if(null!=_&&"#"!==_[0]&&!oe.test(_)){const $=document.querySelector("ion-router");if($)return B?.preventDefault(),$.push(_,q,u)}return!1});return function(B,q,u,$){return c.apply(this,arguments)}}()}}]);