import { Component, ViewChild, ElementRef, AfterViewInit } from '@angular/core';

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  styleUrls: ['home.page.scss'],
  standalone: false,
})
export class HomePage implements AfterViewInit {
  @ViewChild('gameCanvas', { static: false }) canvasRef!: ElementRef<HTMLCanvasElement>;
  
  private canvas!: HTMLCanvasElement;
  private ctx!: CanvasRenderingContext2D;
  
  // Paramètres du jeu
  private readonly squareSize = 30;
  private fallSpeed = 0.5;
  private readonly shooterPos = { x: 400, y: 450 };
  private readonly colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
  private readonly gameHeight = 500; // Zone de jeu
  private readonly uiHeight = 100;   // Zone interface
  private squares: Square[] = [];
  private bullets: Bullet[] = [];
  private angle = 0;
  public score = 0;
  private spawnTimer = 0;
  private gameTimer = 0;
  public level = 1;
  private gameOver = false;
  private gameStarted = false;
  private bestScore = 0;
  private autoShoot = false;
  private autoShootTimer = 0;
  private shooters: {x: number, y: number, angle: number}[] = [];
  private placingShooter = false;
  public money = 0;
  private particles: Particle[] = [];
  private shootAudio = new Audio('assets/shoot.wav');
  private explosionAudio = new Audio('assets/explosion.wav');
  public paused = false;
  public powerUps: PowerUp[] = [];
  private slowTimer = 0;
  // Ajoute un timer pour l'apparition des power-ups
  private powerUpTimer = 0;
  private multiShotTimer = 0;
  private multiShotPowerUpTimer = 0;
  private freezeTimer = 0;
  private freezePowerUpTimer = 0;
  public volume = 1;

  constructor() {}

  ngAfterViewInit() {
    this.canvas = this.canvasRef.nativeElement;
    this.ctx = this.canvas.getContext('2d')!;
    this.loadBestScore();
    this.initGame();
    this.setupEventListeners();
    this.gameLoop();
  }

  private setupEventListeners() {
    // Support tactile ET souris
    this.canvas.addEventListener('touchmove', (event) => {
      event.preventDefault();
      const touch = event.touches[0];
      const rect = this.canvas.getBoundingClientRect();
      const touchX = touch.clientX - rect.left;
      const touchY = touch.clientY - rect.top;
      this.angle = Math.atan2(touchY - this.shooterPos.y, touchX - this.shooterPos.x);
    });

    this.canvas.addEventListener('mousemove', (event) => {
      const rect = this.canvas.getBoundingClientRect();
      const mouseX = event.clientX - rect.left;
      const mouseY = event.clientY - rect.top;
      this.angle = Math.atan2(mouseY - this.shooterPos.y, mouseX - this.shooterPos.x);
    });

    this.canvas.addEventListener('touchstart', (event) => {
      event.preventDefault();
      this.handleClick(event);
    });

    this.canvas.addEventListener('click', (event) => {
      this.handleClick(event);
    });
  }

  private handleClick(event: any) {
    const rect = this.canvas.getBoundingClientRect();
    let clientX, clientY;
    
    if (event.touches) {
      clientX = event.touches[0].clientX;
      clientY = event.touches[0].clientY;
    } else {
      clientX = event.clientX;
      clientY = event.clientY;
    }
    
    const x = clientX - rect.left;
    const y = clientY - rect.top;
    
    // Vérifier si c'est un clic sur le bouton auto-tir
    if (this.gameStarted && !this.gameOver && 
        x >= 10 && x <= 110 &&
        y >= this.gameHeight + 50 && y <= this.gameHeight + 90) {
      this.autoShoot = !this.autoShoot;
      return;
    }
    
    // Vérifier si c'est un clic sur le bouton ajouter lanceur
    if (this.gameStarted && !this.gameOver && this.canAddShooter() &&
        x >= 130 && x <= 230 &&
        y >= this.gameHeight + 50 && y <= this.gameHeight + 90) {
      this.placingShooter = !this.placingShooter;
      return;
    }
    
    // Si on place un lanceur
    if (this.placingShooter && y < this.gameHeight) {
      this.shooters.push({x: x, y: y, angle: 0});
      this.placingShooter = false;
      return;
    }
    
    this.handleShoot(event);
  }

  private handleShoot(event: any) {
    if (!this.gameStarted) {
      const rect = this.canvas.getBoundingClientRect();
      let clientX, clientY;
      
      if (event.touches) {
        clientX = event.touches[0].clientX;
        clientY = event.touches[0].clientY;
      } else {
        clientX = event.clientX;
        clientY = event.clientY;
      }
      
      const x = clientX - rect.left;
      const y = clientY - rect.top;
      
      if (x >= this.canvas.width/2 - 80 && x <= this.canvas.width/2 + 80 &&
          y >= this.canvas.height/2 + 20 && y <= this.canvas.height/2 + 70) {
        this.startGame();
      }
      return;
    }
    
    if (this.gameOver) {
      const rect = this.canvas.getBoundingClientRect();
      let clientX, clientY;
      
      if (event.touches) {
        clientX = event.touches[0].clientX;
        clientY = event.touches[0].clientY;
      } else {
        clientX = event.clientX;
        clientY = event.clientY;
      }
      
      const x = clientX - rect.left;
      const y = clientY - rect.top;
      
      if (x >= this.canvas.width/2 - 80 && x <= this.canvas.width/2 + 80 &&
          y >= this.canvas.height/2 + 50 && y <= this.canvas.height/2 + 100) {
        this.restartGame();
      }
      return;
    }
    
    const bulletSpeed = 8 + (this.fallSpeed * 2);
    if (this.multiShotTimer > 0) {
      // Tir multiple : 3 projectiles en éventail
      for (let i = -1; i <= 1; i++) {
        const angle = this.angle + i * (Math.PI / 16);
        const velocityX = Math.cos(angle) * bulletSpeed;
        const velocityY = Math.sin(angle) * bulletSpeed;
        this.bullets.push(new Bullet(this.shooterPos.x, this.shooterPos.y, velocityX, velocityY));
      }
    } else {
      const velocityX = Math.cos(this.angle) * bulletSpeed;
      const velocityY = Math.sin(this.angle) * bulletSpeed;
      this.bullets.push(new Bullet(this.shooterPos.x, this.shooterPos.y, velocityX, velocityY));
    }
    // Joue le son de tir
    this.shootAudio.currentTime = 0;
    this.shootAudio.play();
  }

  private spawnSquare() {
    const x = Math.random() * (this.canvas.width - this.squareSize);
    const color = this.colors[Math.floor(Math.random() * this.colors.length)];
    
    let maxHits = 1;
    let type: 'normal' | 'splitter' | 'quad-splitter' = 'normal';
    if (this.score >= 1000) {
      // 10% de chance d'avoir un quad-splitter si score > 1000
      if (Math.random() < 0.1) {
        type = 'quad-splitter';
      }
    }
    if (type === 'normal' && this.score >= 500) {
      const rand = Math.random();
      const scoreLevel = Math.floor(this.score / 500);
      const maxPossibleHits = Math.min(2 + scoreLevel, 5);
      
      if (rand < 0.3 + (scoreLevel * 0.1)) {
        maxHits = Math.floor(Math.random() * (maxPossibleHits - 1)) + 2;
      }
      // 20% de chance d'avoir un splitter si score > 500
      if (Math.random() < 0.2) {
        type = 'splitter';
      }
    }
    
    this.squares.push(new Square(x, -this.squareSize, color, maxHits, type));
  }

  private initGame() {
    this.squares = [];
    this.bullets = [];
    this.score = 0;
    this.gameTimer = 0;
    this.level = 1;
    this.fallSpeed = 0.5;
    this.gameOver = false;
    this.gameStarted = false;
    this.autoShoot = false;
    this.autoShootTimer = 0;
    this.shooters = [];
    this.placingShooter = false;
    this.money = 0;
    this.particles = [];
    this.powerUps = [];
    this.slowTimer = 0;
    this.powerUpTimer = 0;
    this.multiShotTimer = 0;
    this.multiShotPowerUpTimer = 0;
    this.freezeTimer = 0;
    this.freezePowerUpTimer = 0;
  }

  private startGame() {
    this.gameStarted = true;
  }

  private loadBestScore() {
    const saved = localStorage.getItem('squareShooterBestScore');
    this.bestScore = saved ? parseInt(saved) : 0;
  }

  private saveBestScore() {
    if (this.score > this.bestScore) {
      this.bestScore = this.score;
      localStorage.setItem('squareShooterBestScore', this.bestScore.toString());
    }
  }

  public restartGame() {
    this.initGame();
  }

  private drawStartScreen() {
    // Arrière-plan dégradé
    const bgGradient = this.ctx.createRadialGradient(
      this.canvas.width/2, this.canvas.height/2, 0,
      this.canvas.width/2, this.canvas.height/2, this.canvas.width
    );
    bgGradient.addColorStop(0, '#667eea');
    bgGradient.addColorStop(1, '#764ba2');
    this.ctx.fillStyle = bgGradient;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Titre avec effet néon
    this.ctx.font = 'bold 42px "Segoe UI", Arial';
    this.ctx.textAlign = 'center';
    
    // Effet de lueur
    this.ctx.shadowColor = '#00FFFF';
    this.ctx.shadowBlur = 20;
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.fillText('🎯 SQUARE SHOOTER', this.canvas.width/2, this.canvas.height/2 - 80);
    
    this.ctx.shadowColor = 'transparent';
    
    // Sous-titre
    this.ctx.font = '18px "Segoe UI", Arial';
    this.ctx.fillStyle = '#E8F4FD';
    this.ctx.fillText('Visez et détruisez les carrés qui tombent!', this.canvas.width/2, this.canvas.height/2 - 20);
    
    // Bouton moderne
    this.drawModernButton(this.canvas.width/2 - 80, this.canvas.height/2 + 20, 160, 50, '#27AE60', 'COMMENCER');
    
    this.ctx.textAlign = 'left';
  }

  private drawGameOver() {
    // Arrière-plan dégradé sombre
    const bgGradient = this.ctx.createRadialGradient(
      this.canvas.width/2, this.canvas.height/2, 0,
      this.canvas.width/2, this.canvas.height/2, this.canvas.width
    );
    bgGradient.addColorStop(0, '#2C3E50');
    bgGradient.addColorStop(1, '#000000');
    this.ctx.fillStyle = bgGradient;
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Titre GAME OVER avec effet
    this.ctx.font = 'bold 44px "Segoe UI", Arial';
    this.ctx.textAlign = 'center';
    
    this.ctx.shadowColor = '#E74C3C';
    this.ctx.shadowBlur = 15;
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.fillText('💀 GAME OVER', this.canvas.width/2, this.canvas.height/2 - 120);
    
    this.ctx.shadowColor = 'transparent';
    
    // Score final
    this.ctx.font = 'bold 24px "Segoe UI", Arial';
    this.ctx.fillStyle = '#F1C40F';
    this.ctx.fillText('🎯 Score Final: ' + this.score, this.canvas.width/2, this.canvas.height/2 - 70);
    
    // Meilleur score
    this.ctx.font = '20px "Segoe UI", Arial';
    this.ctx.fillStyle = this.score >= this.bestScore ? '#E67E22' : '#95A5A6';
    this.ctx.fillText('🏆 Meilleur: ' + this.bestScore, this.canvas.width/2, this.canvas.height/2 - 40);
    
    // Nouveau record
    if (this.score === this.bestScore && this.score > 0) {
      this.ctx.font = 'bold 18px "Segoe UI", Arial';
      this.ctx.shadowColor = '#F1C40F';
      this.ctx.shadowBlur = 10;
      this.ctx.fillStyle = '#FFD700';
      this.ctx.fillText('✨ NOUVEAU RECORD! ✨', this.canvas.width/2, this.canvas.height/2 - 10);
      this.ctx.shadowColor = 'transparent';
    }
    
    // Niveau atteint
    this.ctx.font = '18px "Segoe UI", Arial';
    this.ctx.fillStyle = '#3498DB';
    this.ctx.fillText('⚡ Niveau: ' + this.level, this.canvas.width/2, this.canvas.height/2 + 20);
    
    // Bouton rejouer
    this.drawModernButton(this.canvas.width/2 - 80, this.canvas.height/2 + 50, 160, 50, '#27AE60', 'REJOUER');
    
    this.ctx.textAlign = 'left';
  }

  private updateDifficulty() {
    this.gameTimer++;
    const newLevel = Math.floor(this.score / 300) + 1;
    if (newLevel > this.level) {
      this.level = newLevel;
      this.fallSpeed = 0.5 + (this.level - 1) * 0.3;
    }
  }

  private drawShooter() {
    // Canon avec gradient
    const gradient = this.ctx.createLinearGradient(
      this.shooterPos.x, this.shooterPos.y - 5,
      this.shooterPos.x, this.shooterPos.y + 5
    );
    gradient.addColorStop(0, '#34495E');
    gradient.addColorStop(1, '#2C3E50');
    
    this.ctx.beginPath();
    this.ctx.moveTo(this.shooterPos.x, this.shooterPos.y);
    this.ctx.lineTo(this.shooterPos.x + Math.cos(this.angle) * 45, this.shooterPos.y + Math.sin(this.angle) * 45);
    this.ctx.strokeStyle = gradient;
    this.ctx.lineWidth = 10;
    this.ctx.lineCap = 'round';
    this.ctx.stroke();
    this.ctx.closePath();

    // Base du shooter avec effet 3D
    const baseGradient = this.ctx.createRadialGradient(
      this.shooterPos.x, this.shooterPos.y, 0,
      this.shooterPos.x, this.shooterPos.y, 18
    );
    baseGradient.addColorStop(0, '#5DADE2');
    baseGradient.addColorStop(1, '#3498DB');
    
    this.ctx.shadowColor = 'rgba(0,0,0,0.3)';
    this.ctx.shadowBlur = 6;
    this.ctx.shadowOffsetY = 3;
    
    this.ctx.beginPath();
    this.ctx.arc(this.shooterPos.x, this.shooterPos.y, 18, 0, Math.PI * 2);
    this.ctx.fillStyle = baseGradient;
    this.ctx.fill();
    
    this.ctx.shadowColor = 'transparent';
    this.ctx.strokeStyle = '#2980B9';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();
    this.ctx.closePath();
  }

  private checkCollisions() {
    for (let i = this.bullets.length - 1; i >= 0; i--) {
      for (let j = this.squares.length - 1; j >= 0; j--) {
        const bulletRadius = 6;
        const squareCenterX = this.squares[j].x + this.squares[j].size / 2;
        const squareCenterY = this.squares[j].y + this.squares[j].size / 2;
        const distance = Math.sqrt((this.bullets[i].x - squareCenterX) ** 2 + (this.bullets[i].y - squareCenterY) ** 2);
        const collision = distance <= (this.squares[j].size / 2 + bulletRadius);
        if (collision) {
          this.squares[j].hits--;
          this.squares[j].hitAnimation = 5; // effet pop pendant 5 frames
          this.bullets.splice(i, 1);
          
          if (this.squares[j].hits <= 0) {
            const zoneBonus = this.getZoneBonus(this.squares[j].y);
            const hitBonus = this.squares[j].maxHits > 1 ? this.squares[j].maxHits * 5 : 0;
            // Générer des particules d’explosion
            for (let p = 0; p < 18; p++) {
              const angle = Math.random() * Math.PI * 2;
              const speed = 2 + Math.random() * 2;
              this.particles.push(new Particle(
                this.squares[j].x + (this.squares[j].size || this.squareSize)/2,
                this.squares[j].y + (this.squares[j].size || this.squareSize)/2,
                this.squares[j].color,
                Math.cos(angle) * speed,
                Math.sin(angle) * speed,
                18 + Math.random() * 10,
                3 + Math.random() * 3
              ));
            }
            // Si c'est un quad-splitter, génère 4 petits carrés
            if (this.squares[j].type === 'quad-splitter') {
              for (let s = 0; s < 4; s++) {
                const angle = (Math.PI/2) * s;
                const offsetX = Math.cos(angle) * 16;
                const offsetY = Math.sin(angle) * 16;
                this.squares.push(new Square(
                  this.squares[j].x + offsetX,
                  this.squares[j].y + offsetY,
                  this.colors[Math.floor(Math.random() * this.colors.length)],
                  1,
                  'normal',
                  16 // taille plus petite
                ));
              }
            }
            // Si c'est un splitter, génère deux petits carrés
            if (this.squares[j].type === 'splitter') {
              for (let s = 0; s < 2; s++) {
                const offset = (s === 0 ? -1 : 1) * 12;
                this.squares.push(new Square(
                  this.squares[j].x + offset,
                  this.squares[j].y,
                  this.colors[Math.floor(Math.random() * this.colors.length)],
                  1,
                  'normal',
                  18 // taille plus petite
                ));
              }
            }
            this.explosionAudio.currentTime = 0;
            this.explosionAudio.play();
            this.squares.splice(j, 1);
            this.score += zoneBonus.points + hitBonus;
            this.money += zoneBonus.money + (hitBonus > 0 ? 1 : 0);
          }
          break;
        }
      }
    }
  }

  private gameLoop = () => {
    if (this.paused) return;
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    if (!this.gameStarted) {
      this.drawStartScreen();
      requestAnimationFrame(this.gameLoop);
      return;
    }
    
    if (this.gameOver) {
      this.drawGameOver();
      requestAnimationFrame(this.gameLoop);
      return;
    }
    
    this.updateDifficulty();
    
    // DESSINER LE FOND EN PREMIER !
    this.drawGameArea();
    
    // Apparition d'un power-up toutes les 30 secondes si score >= 1000
    if (this.score >= 1000) {
      this.powerUpTimer++;
      if (this.powerUpTimer >= 1800 && this.powerUps.length < 1) {
        const type = Math.random() < 0.5 ? 'bomb' : 'slow';
        const x = 40 + Math.random() * (this.canvas.width - 80);
        this.powerUps.push(new PowerUp(x, -30, type));
        this.powerUpTimer = 0;
      }
    } else {
      this.powerUpTimer = 0;
    }

    // Apparition du power-up multi-shot toutes les 40 secondes si score >= 1500
    if (this.score >= 1500) {
      this.multiShotPowerUpTimer++;
      if (this.multiShotPowerUpTimer >= 2400 && !this.powerUps.some(p => p.type === 'multi-shot')) {
        const x = 40 + Math.random() * (this.canvas.width - 80);
        this.powerUps.push(new PowerUp(x, -30, 'multi-shot'));
        this.multiShotPowerUpTimer = 0;
      }
    } else {
      this.multiShotPowerUpTimer = 0;
    }

    // Apparition d'un power-up freeze toutes les 45 secondes si score >= 2000
    if (this.score >= 2000) {
      this.freezePowerUpTimer++;
      if (this.freezePowerUpTimer >= 2700 && !this.powerUps.some(p => p.type === 'freeze')) {
        const x = 40 + Math.random() * (this.canvas.width - 80);
        this.powerUps.push(new PowerUp(x, -30, 'freeze'));
        this.freezePowerUpTimer = 0;
      }
    } else {
      this.freezePowerUpTimer = 0;
    }

    // Affichage et mouvement des power-ups
    for (let i = this.powerUps.length - 1; i >= 0; i--) {
      this.powerUps[i].move();
      this.powerUps[i].draw(this.ctx);
      // Supprime si sort du canvas
      if (this.powerUps[i].y > this.canvas.height + 40) {
        this.powerUps.splice(i, 1);
      }
    }

    // Affichage de l'icône multi-shot si actif
    if (this.multiShotTimer > 0) {
      this.ctx.save();
      this.ctx.font = 'bold 32px Arial';
      this.ctx.globalAlpha = 0.92;
      this.ctx.fillText('🔫', this.canvas.width - 48, 48);
      this.ctx.restore();
      this.multiShotTimer--;
    }

    // Affichage de l'icône freeze si actif
    if (this.freezeTimer > 0) {
      this.ctx.save();
      this.ctx.font = 'bold 32px Arial';
      this.ctx.globalAlpha = 0.92;
      this.ctx.fillText('❄️', this.canvas.width - 96, 48);
      this.ctx.restore();
      this.freezeTimer--;
    }

    // Ralentissement temporaire
    if (this.slowTimer > 0) {
      this.slowTimer--;
      if (this.slowTimer === 0) {
        this.fallSpeed = 0.5 + Math.floor(this.score / 500) * 0.1;
      }
    }

    this.spawnTimer++;
    if (this.spawnTimer > 60) {
      this.spawnSquare();
      this.spawnTimer = 0;
    }
    
    for (let i = this.squares.length - 1; i >= 0; i--) {
      if (this.freezeTimer === 0) {
        this.squares[i].move(this.fallSpeed);
      }
      this.squares[i].draw(this.ctx, this.squares[i].size || this.squareSize);
      
      if (this.squares[i].y >= this.gameHeight - 10) {
        this.saveBestScore();
        this.gameOver = true;
        break;
      }
    }
    
    for (let i = this.bullets.length - 1; i >= 0; i--) {
      this.bullets[i].move();
      this.bullets[i].draw(this.ctx);
      
      if (this.bullets[i].x < 0 || this.bullets[i].x > this.canvas.width || 
          this.bullets[i].y < 0 || this.bullets[i].y > this.canvas.height) {
        this.bullets.splice(i, 1);
      }
    }
    // Affichage et animation des particules
    for (let i = this.particles.length - 1; i >= 0; i--) {
      this.particles[i].move();
      this.particles[i].draw(this.ctx);
      if (this.particles[i].life <= 0) {
        this.particles.splice(i, 1);
      }
    }
    this.drawShooter();
    this.drawAllShooters();
    this.drawUI();
    this.handleAutoShoot();
    this.checkCollisions();
    this.checkPowerUpCollisions();
    requestAnimationFrame(this.gameLoop);
  }

  private handleAutoShoot() {
    if (this.autoShoot && this.squares.length > 0) {
      this.autoShootTimer++;
      if (this.autoShootTimer >= 20) {
        const target = this.findClosestSquare();
        if (target) {
          // Tir du lanceur principal
          const targetX = target.x + this.squareSize / 2;
          const targetY = target.y + this.squareSize / 2;
          const autoAngle = Math.atan2(targetY - this.shooterPos.y, targetX - this.shooterPos.x);
          
          const bulletSpeed = 8 + (this.fallSpeed * 2);
          const velocityX = Math.cos(autoAngle) * bulletSpeed;
          const velocityY = Math.sin(autoAngle) * bulletSpeed;
          this.bullets.push(new Bullet(this.shooterPos.x, this.shooterPos.y, velocityX, velocityY));
          
          // Tir des autres lanceurs
          for (let shooter of this.shooters) {
            const autoAngle2 = Math.atan2(targetY - shooter.y, targetX - shooter.x);
            shooter.angle = autoAngle2;
            
            const bulletSpeed = 8 + (this.fallSpeed * 2);
            const velocityX2 = Math.cos(autoAngle2) * bulletSpeed;
            const velocityY2 = Math.sin(autoAngle2) * bulletSpeed;
            this.bullets.push(new Bullet(shooter.x, shooter.y, velocityX2, velocityY2));
          }
        }
        this.autoShootTimer = 0;
      }
    }
  }

  private findClosestSquare(): Square | null {
    if (this.squares.length === 0) return null;
    
    let closest = this.squares[0];
    let minDistance = this.getDistance(this.shooterPos.x, this.shooterPos.y, closest.x + this.squareSize/2, closest.y + this.squareSize/2);
    
    for (let square of this.squares) {
      const distance = this.getDistance(this.shooterPos.x, this.shooterPos.y, square.x + this.squareSize/2, square.y + this.squareSize/2);
      if (distance < minDistance) {
        minDistance = distance;
        closest = square;
      }
    }
    
    return closest;
  }

  private getDistance(x1: number, y1: number, x2: number, y2: number): number {
    return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
  }

  private getZoneBonus(squareY: number): {points: number, money: number} {
    const midZone = this.gameHeight / 2;
    
    if (squareY >= midZone) {
      // Zone basse = plus de points et argent
      return {points: 15, money: 2};
    } else {
      // Zone haute = moins de points, pas d'argent
      return {points: 10, money: 0};
    }
  }



  private canAddShooter(): boolean {
    const maxShooters = Math.floor(this.score / 500);
    return this.shooters.length < maxShooters;
  }

  private drawGameArea() {
    // Arrière-plan dégradé pour la zone de jeu
    const bgGradient = this.ctx.createLinearGradient(0, 0, 0, this.gameHeight);
    bgGradient.addColorStop(0, '#1a1a2e');
    bgGradient.addColorStop(1, '#16213e');
    this.ctx.fillStyle = bgGradient;
    this.ctx.fillRect(0, 0, this.canvas.width, this.gameHeight);
    
    // Ligne de séparation moderne
    const lineGradient = this.ctx.createLinearGradient(0, this.gameHeight, this.canvas.width, this.gameHeight);
    lineGradient.addColorStop(0, 'transparent');
    lineGradient.addColorStop(0.5, '#E74C3C');
    lineGradient.addColorStop(1, 'transparent');
    
    this.ctx.strokeStyle = lineGradient;
    this.ctx.lineWidth = 4;
    this.ctx.beginPath();
    this.ctx.moveTo(0, this.gameHeight);
    this.ctx.lineTo(this.canvas.width, this.gameHeight);
    this.ctx.stroke();
    
    // Zone d'interface avec gradient
    const uiGradient = this.ctx.createLinearGradient(0, this.gameHeight, 0, this.canvas.height);
    uiGradient.addColorStop(0, '#2C3E50');
    uiGradient.addColorStop(1, '#34495E');
    this.ctx.fillStyle = uiGradient;
    this.ctx.fillRect(0, this.gameHeight, this.canvas.width, this.uiHeight);
  }

  private drawAllShooters() {
    for (let shooter of this.shooters) {
      this.ctx.beginPath();
      this.ctx.moveTo(shooter.x, shooter.y);
      this.ctx.lineTo(shooter.x + Math.cos(shooter.angle) * 30, 
                      shooter.y + Math.sin(shooter.angle) * 30);
      this.ctx.strokeStyle = 'blue';
      this.ctx.lineWidth = 6;
      this.ctx.stroke();
      this.ctx.closePath();

      this.ctx.beginPath();
      this.ctx.arc(shooter.x, shooter.y, 12, 0, Math.PI * 2);
      this.ctx.fillStyle = 'darkblue';
      this.ctx.fill();
      this.ctx.closePath();
    }
  }

  private drawUI() {
    const uiY = this.gameHeight;
    
    // Informations du jeu avec style moderne
    this.ctx.font = 'bold 18px "Segoe UI", Arial';
    this.ctx.fillStyle = '#ECF0F1';
    this.ctx.fillText('🎯 ' + this.score, 10, uiY + 25);
    this.ctx.fillText('⚡ ' + this.level, 150, uiY + 25);
    this.ctx.fillText('💰 ' + this.money, 280, uiY + 25);
    
    // Bouton Auto-tir moderne
    this.drawModernButton(10, uiY + 40, 90, 35, 
      this.autoShoot ? '#E74C3C' : '#7F8C8D', 
      this.autoShoot ? 'AUTO ON' : 'AUTO OFF');
    
    // Bouton Ajouter Lanceur
    if (this.canAddShooter()) {
      this.drawModernButton(110, uiY + 40, 90, 35,
        this.placingShooter ? '#F39C12' : '#3498DB',
        this.placingShooter ? 'PLACER' : 'AJOUTER');
    }
  }
  
  private drawModernButton(x: number, y: number, width: number, height: number, color: string, text: string) {
    // Ombre du bouton
    this.ctx.shadowColor = 'rgba(0,0,0,0.3)';
    this.ctx.shadowBlur = 4;
    this.ctx.shadowOffsetY = 2;
    
    // Gradient du bouton
    const gradient = this.ctx.createLinearGradient(x, y, x, y + height);
    gradient.addColorStop(0, color);
    gradient.addColorStop(1, this.darkenColor(color, 0.2));
    
    // Bouton arrondi
    this.ctx.fillStyle = gradient;
    this.roundRect(this.ctx, x, y, width, height, 8);
    this.ctx.fill();
    
    this.ctx.shadowColor = 'transparent';
    
    // Bordure
    this.ctx.strokeStyle = this.darkenColor(color, 0.3);
    this.ctx.lineWidth = 2;
    this.roundRect(this.ctx, x, y, width, height, 8);
    this.ctx.stroke();
    
    // Texte
    this.ctx.font = 'bold 12px "Segoe UI", Arial';
    this.ctx.fillStyle = '#FFFFFF';
    this.ctx.textAlign = 'center';
    this.ctx.fillText(text, x + width/2, y + height/2 + 4);
    this.ctx.textAlign = 'left';
  }
  
  private roundRect(ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, radius: number) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  }
  
  private darkenColor(color: string, amount: number): string {
    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(255 * amount);
    const R = (num >> 16) - amt;
    const G = (num >> 8 & 0x00FF) - amt;
    const B = (num & 0x0000FF) - amt;
    return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
  }

  public togglePause() {
    this.paused = !this.paused;
    if (!this.paused) {
      requestAnimationFrame(this.gameLoop);
    }
  }

  public updateVolume() {
    this.shootAudio.volume = this.volume;
    this.explosionAudio.volume = this.volume;
  }

  public checkPowerUpCollisions() {
    for (let i = this.bullets.length - 1; i >= 0; i--) {
      for (let j = this.powerUps.length - 1; j >= 0; j--) {
        const dx = this.bullets[i].x - this.powerUps[j].x;
        const dy = this.bullets[i].y - this.powerUps[j].y;
        const dist = Math.sqrt(dx * dx + dy * dy);
        if (dist < 28) {
          // Effet bombe
          if (this.powerUps[j].type === 'bomb') {
            // Explosion de tous les carrés
            for (let k = this.squares.length - 1; k >= 0; k--) {
              // Génère des particules pour chaque carré
              for (let p = 0; p < 12; p++) {
                const angle = Math.random() * Math.PI * 2;
                const speed = 2 + Math.random() * 2;
                this.particles.push(new Particle(
                  this.squares[k].x + (this.squares[k].size || this.squareSize)/2,
                  this.squares[k].y + (this.squares[k].size || this.squareSize)/2,
                  this.squares[k].color,
                  Math.cos(angle) * speed,
                  Math.sin(angle) * speed,
                  14 + Math.random() * 8,
                  2 + Math.random() * 2
                ));
              }
            }
            this.squares = [];
          }
          // Effet ralentisseur
          if (this.powerUps[j].type === 'slow') {
            this.fallSpeed = 0.15;
            this.slowTimer = 300; // 5 secondes à 60 FPS
          }
          // Effet multi-shot
          if (this.powerUps[j].type === 'multi-shot') {
            this.multiShotTimer = 300; // 5 secondes à 60 FPS
          }
          // Effet freeze
          if (this.powerUps[j].type === 'freeze') {
            this.freezeTimer = 180; // 3 secondes à 60 FPS
          }
          this.powerUps.splice(j, 1);
          this.bullets.splice(i, 1);
          break;
        }
      }
    }
  }

  public toggleAutoShoot() {
    this.autoShoot = !this.autoShoot;
  }
}

class Square {
  public hits: number;
  public maxHits: number;
  public hitAnimation: number = 0; // 0 = normal, >0 = effet pop
  public type: 'normal' | 'splitter' | 'quad-splitter';
  public size: number;

  constructor(public x: number, public y: number, public color: string, maxHits: number = 1, type: 'normal' | 'splitter' | 'quad-splitter' = 'normal', size: number = 30) {
    this.maxHits = maxHits;
    this.hits = maxHits;
    this.type = type;
    this.size = size;
  }

  move(fallSpeed: number) {
    this.y += fallSpeed;
    if (this.hitAnimation > 0) {
      this.hitAnimation--;
    }
  }

  draw(ctx: CanvasRenderingContext2D, size: number) {
    // Utilise this.size si précisé
    size = this.size || size;
    const radius = 8;
    let scale = 1;
    if (this.hitAnimation > 0) {
      scale = 1.25 - 0.25 * (this.hitAnimation / 5);
    }
    const drawX = this.x + size/2 - (size*scale)/2;
    const drawY = this.y + size/2 - (size*scale)/2;
    const drawSize = size * scale;
    ctx.save();
    ctx.shadowColor = this.color;
    ctx.shadowBlur = 18;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    const gradient = ctx.createLinearGradient(drawX, drawY, drawX, drawY + drawSize);
    gradient.addColorStop(0, this.color);
    gradient.addColorStop(1, this.darkenColor(this.color, 0.3));
    ctx.fillStyle = gradient;
    this.roundRect(ctx, drawX, drawY, drawSize, drawSize, radius);
    ctx.fill();
    ctx.restore();
    ctx.save();
    ctx.lineWidth = 4;
    ctx.strokeStyle = '#fff';
    this.roundRect(ctx, drawX, drawY, drawSize, drawSize, radius);
    ctx.stroke();
    ctx.restore();
    ctx.save();
    ctx.lineWidth = this.hits > 1 ? 3 : 2;
    ctx.strokeStyle = this.hits > 1 ? '#FF3838' : '#2C3E50';
    this.roundRect(ctx, drawX, drawY, drawSize, drawSize, radius);
    ctx.stroke();
    ctx.restore();
    if (this.hits > 1) {
      ctx.save();
      ctx.fillStyle = '#FFFFFF';
      ctx.font = 'bold 14px "Orbitron", Arial';
      ctx.textAlign = 'center';
      ctx.strokeStyle = '#000000';
      ctx.lineWidth = 2;
      ctx.strokeText(this.hits.toString(), drawX + drawSize/2, drawY + drawSize/2 + 4);
      ctx.fillText(this.hits.toString(), drawX + drawSize/2, drawY + drawSize/2 + 4);
      ctx.restore();
    }
    // Affiche un symbole spécial pour les splitters
    if (this.type === 'splitter') {
      ctx.save();
      ctx.font = 'bold 18px "Orbitron", Arial';
      ctx.fillStyle = '#00fff7';
      ctx.textAlign = 'center';
      ctx.fillText('✦', drawX + drawSize/2, drawY + drawSize/2 + 7);
      ctx.restore();
    }
    if (this.type === 'quad-splitter') {
      ctx.save();
      ctx.font = 'bold 18px "Orbitron", Arial';
      ctx.fillStyle = '#FFD700';
      ctx.textAlign = 'center';
      ctx.fillText('✸', drawX + drawSize/2, drawY + drawSize/2 + 7);
      ctx.restore();
    }
  }
  
  private roundRect(ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, radius: number) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
  }
  
  private darkenColor(color: string, amount: number): string {
    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(255 * amount);
    const R = (num >> 16) - amt;
    const G = (num >> 8 & 0x00FF) - amt;
    const B = (num & 0x0000FF) - amt;
    return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
  }
}

class Bullet {
  constructor(
    public x: number, 
    public y: number, 
    public velocityX: number, 
    public velocityY: number
  ) {}

  move() {
    this.x += this.velocityX;
    this.y += this.velocityY;
  }

  draw(ctx: CanvasRenderingContext2D) {
    const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, 6);
    gradient.addColorStop(0, '#FFD700');
    gradient.addColorStop(1, '#FF8C00');
    
    ctx.shadowColor = 'rgba(255, 215, 0, 0.5)';
    ctx.shadowBlur = 8;
    
    ctx.beginPath();
    ctx.arc(this.x, this.y, 6, 0, Math.PI * 2);
    ctx.fillStyle = gradient;
    ctx.fill();
    
    ctx.shadowColor = 'transparent';
    ctx.strokeStyle = '#B8860B';
    ctx.lineWidth = 1;
    ctx.stroke();
    ctx.closePath();
  }
}

// Ajout de la classe Particle
class Particle {
  constructor(
    public x: number,
    public y: number,
    public color: string,
    public vx: number,
    public vy: number,
    public life: number,
    public size: number
  ) {}

  move() {
    this.x += this.vx;
    this.y += this.vy;
    this.life--;
    this.vy += 0.1; // gravité
  }

  draw(ctx: CanvasRenderingContext2D) {
    ctx.save();
    ctx.globalAlpha = Math.max(this.life / 20, 0);
    ctx.fillStyle = this.color;
    ctx.beginPath();
    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
    ctx.fill();
    ctx.restore();
  }
}

// Classe PowerUp
class PowerUp {
  constructor(
    public x: number,
    public y: number,
    public type: 'bomb' | 'slow' | 'multi-shot' | 'freeze',
    public vy: number = 2
  ) {}

  move() {
    this.y += this.vy;
  }

  draw(ctx: CanvasRenderingContext2D) {
    ctx.save();
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.globalAlpha = 0.92;
    if (this.type === 'bomb') {
      ctx.fillText('💣', this.x, this.y);
    } else if (this.type === 'slow') {
      ctx.fillText('🐢', this.x, this.y);
    } else if (this.type === 'multi-shot') {
      ctx.fillText('🔫', this.x, this.y);
    } else if (this.type === 'freeze') {
      ctx.fillText('❄️', this.x, this.y);
    }
    ctx.restore();
  }
}