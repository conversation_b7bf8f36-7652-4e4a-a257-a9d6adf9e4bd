<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>
      Square Shooter Game
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="game-container">
    <canvas #gameCanvas width="800" height="600"></canvas>
    <!-- Overlay Pause modernisé -->
    <div class="pause-overlay" *ngIf="paused">
      <div class="pause-menu">
        <h2><span style="font-size:2.2rem;">⏸️</span> Pause</h2>
        <button class="modern-btn big-btn" (click)="togglePause()">▶️ Reprendre</button>
        <button class="modern-btn big-btn" (click)="restartGame()">🔄 Recommencer</button>
        <div class="config-section">
          <h3>Configuration</h3>
          <div class="volume-control">
            <span style="font-size:1.5rem;">🔊</span>
            <input id="volume" type="range" min="0" max="1" step="0.01" [(ngModel)]="volume" (input)="updateVolume()" />
            <span>{{ (volume * 100) | number:'1.0-0' }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</ion-content>
