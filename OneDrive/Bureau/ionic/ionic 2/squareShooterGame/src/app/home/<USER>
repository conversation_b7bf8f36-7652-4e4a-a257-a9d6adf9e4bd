.game-container {
  max-width: 400px;
  width: 100vw;
  height: 800px;
  margin: 32px auto;
  box-shadow: 0 0 32px #0008;
  border-radius: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f2027 0%, #2c5364 50%, #ff00cc 100%);
  /* Motif grille néon */
  background-image: repeating-linear-gradient(135deg, rgba(255,255,255,0.03) 0 2px, transparent 2px 40px);
}

canvas {
  border: none;
  border-radius: 24px;
  box-shadow: 0 0 32px 8px #00fff7, 0 1.5px 8px rgba(0,0,0,0.12);
  max-width: 98vw;
  max-height: 80vh;
  background: rgba(0,0,0,0.15);
  backdrop-filter: blur(2px);
  transition: box-shadow 0.3s;
}

.score-badge {
  position: absolute;
  top: 32px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(90deg, #00fff7 0%, #ff00cc 100%);
  color: #fff;
  font-family: 'Orbitron', Arial, sans-serif;
  font-size: 2rem;
  font-weight: 700;
  padding: 12px 40px;
  border-radius: 40px;
  box-shadow: 0 0 24px #00fff7, 0 2px 8px rgba(255,0,204,0.18);
  letter-spacing: 2px;
  z-index: 10;
  border: 2px solid #fff;
  text-shadow: 0 0 8px #ff00cc, 0 0 2px #fff;
}

.modern-btn {
  background: linear-gradient(90deg, #00fff7 0%, #ff00cc 100%);
  color: #fff;
  border: none;
  border-radius: 24px;
  padding: 14px 36px;
  font-family: 'Orbitron', Arial, sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  box-shadow: 0 0 16px #00fff7, 0 2px 8px rgba(255,0,204,0.18);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
  margin: 12px 0;
  border: 2px solid #fff;
  text-shadow: 0 0 8px #ff00cc, 0 0 2px #fff;
}
.modern-btn:hover {
  background: linear-gradient(90deg, #ff00cc 0%, #00fff7 100%);
  box-shadow: 0 0 32px #ff00cc, 0 4px 16px #00fff7;
  transform: translateY(-2px) scale(1.06);
}

.score-pause-bar {
  position: absolute;
  top: 32px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 20px;
  z-index: 20;
}

.pause-btn {
  background: linear-gradient(90deg, #00fff7 0%, #ff00cc 100%);
  color: #fff;
  border: 2px solid #fff;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  font-size: 2rem;
  box-shadow: 0 0 16px #00fff7, 0 2px 8px rgba(255,0,204,0.18);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s, transform 0.1s;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.pause-btn:hover {
  background: linear-gradient(90deg, #ff00cc 0%, #00fff7 100%);
  box-shadow: 0 0 32px #ff00cc, 0 4px 16px #00fff7;
  transform: scale(1.08);
}

.pause-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(10, 10, 30, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.pause-menu {
  background: linear-gradient(135deg, #0f2027 0%, #2c5364 50%, #ff00cc 100%);
  border-radius: 32px;
  padding: 48px 32px 32px 32px;
  box-shadow: 0 0 32px #00fff7, 0 2px 8px #ff00cc;
  text-align: center;
  color: #fff;
  min-width: 340px;
  max-width: 90vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.pause-menu h2 {
  font-family: 'Orbitron', Arial, sans-serif;
  font-size: 2.2rem;
  margin-bottom: 32px;
  text-shadow: 0 0 8px #ff00cc, 0 0 2px #fff;
  letter-spacing: 2px;
}
.big-btn {
  font-size: 1.3rem;
  padding: 18px 48px;
  margin: 16px 0 0 0;
  border-radius: 32px;
  width: 100%;
  max-width: 320px;
  box-shadow: 0 0 16px #00fff7, 0 2px 8px #ff00cc;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}
.config-section {
  margin-top: 36px;
  width: 100%;
  background: rgba(0,0,0,0.10);
  border-radius: 20px;
  padding: 18px 0 8px 0;
  box-shadow: 0 0 8px #00fff744;
}
.config-section h3 {
  font-size: 1.1rem;
  margin-bottom: 12px;
  color: #fff;
  letter-spacing: 1px;
}
.volume-control {
  margin: 0 0 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #fff;
}
.volume-control input[type="range"] {
  width: 120px;
}